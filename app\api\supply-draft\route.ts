import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';

// GET - استرجاع المسودات المحفوظة
export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || authResult.user!.id.toString();

    // استرجاع المسودات للمستخدم الحالي
    const drafts = await prisma.supplyOrderDraft.findMany({
      where: {
        userId: parseInt(userId)
      },
      orderBy: {
        updatedAt: 'desc'
      }
    });

    return NextResponse.json({
      success: true,
      drafts
    });

  } catch (error) {
    console.error('Failed to fetch supply drafts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch supply drafts' },
      { status: 500 }
    );
  }
}

// POST - حفظ مسودة جديدة أو تحديث موجودة
export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const draftData = await request.json();

    // التحقق من البيانات الأساسية
    if (!draftData.formState && !draftData.currentItems) {
      return NextResponse.json(
        { error: 'Draft data is required' },
        { status: 400 }
      );
    }

    const userId = authResult.user!.id;

    // البحث عن مسودة موجودة للمستخدم
    const existingDraft = await prisma.supplyOrderDraft.findFirst({
      where: {
        userId: userId
      }
    });

    let savedDraft;

    if (existingDraft) {
      // تحديث المسودة الموجودة
      savedDraft = await prisma.supplyOrderDraft.update({
        where: {
          id: existingDraft.id
        },
        data: {
          formState: JSON.stringify(draftData.formState || {}),
          currentItems: JSON.stringify(draftData.currentItems || []),
          attachments: JSON.stringify(draftData.attachments || []),
          supplyOrderId: draftData.supplyOrderId || null,
          updatedAt: new Date()
        }
      });
    } else {
      // إنشاء مسودة جديدة
      savedDraft = await prisma.supplyOrderDraft.create({
        data: {
          userId: userId,
          formState: JSON.stringify(draftData.formState || {}),
          currentItems: JSON.stringify(draftData.currentItems || []),
          attachments: JSON.stringify(draftData.attachments || []),
          supplyOrderId: draftData.supplyOrderId || null
        }
      });
    }

    return NextResponse.json({
      success: true,
      draft: savedDraft,
      message: 'تم حفظ المسودة في قاعدة البيانات بنجاح'
    });

  } catch (error) {
    console.error('Failed to save supply draft:', error);
    return NextResponse.json(
      { error: 'Failed to save supply draft' },
      { status: 500 }
    );
  }
}

// DELETE - حذف مسودة
export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const draftId = searchParams.get('draftId');
    const userId = authResult.user!.id;

    if (draftId) {
      // حذف مسودة محددة
      await prisma.supplyOrderDraft.delete({
        where: {
          id: parseInt(draftId),
          userId: userId // التأكد من أن المسودة تخص المستخدم الحالي
        }
      });
    } else {
      // حذف جميع مسودات المستخدم
      await prisma.supplyOrderDraft.deleteMany({
        where: {
          userId: userId
        }
      });
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف المسودة بنجاح'
    });

  } catch (error) {
    console.error('Failed to delete supply draft:', error);
    return NextResponse.json(
      { error: 'Failed to delete supply draft' },
      { status: 500 }
    );
  }
}
