// سكريبت اختبار للتأكد من أن جميع حقول التاريخ تعمل بشكل صحيح مع DateTime

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDateTimeFields() {
  try {
    console.log('🧪 بدء اختبار حقول DateTime...\n');

    const timestamp = Date.now();

    // اختبار إنشاء مبيعة جديدة
    console.log('1️⃣ اختبار إنشاء مبيعة جديدة...');
    const testSale = await prisma.sale.create({
      data: {
        soNumber: `TEST-SO-${timestamp}`,
        opNumber: `TEST-OP-${timestamp}`,
        date: new Date(), // استخدام DateTime
        clientName: 'عميل تجريبي',
        warehouseName: 'المخزن الرئيسي',
        employeeName: 'موظف تجريبي',
        warrantyPeriod: 'none' // إضافة الحقل المطلوب
      }
    });
    console.log('✅ تم إنشاء المبيعة بنجاح، ID:', testSale.id);
    console.log('   التاريخ المحفوظ:', testSale.date);
    console.log('   نوع البيانات:', typeof testSale.date);

    // اختبار إنشاء مرتجع جديد
    console.log('\n2️⃣ اختبار إنشاء مرتجع جديد...');
    const testReturn = await prisma.return.create({
      data: {
        roNumber: `TEST-RO-${timestamp}`,
        opReturnNumber: `TEST-OP-RO-${timestamp}`,
        date: new Date(), // استخدام DateTime
        saleId: testSale.id,
        soNumber: testSale.soNumber, // إضافة رقم المبيعة
        clientName: 'عميل مرتجع تجريبي',
        reason: 'اختبار النظام'
      }
    });
    console.log('✅ تم إنشاء المرتجع بنجاح، ID:', testReturn.id);
    console.log('   التاريخ المحفوظ:', testReturn.date);
    console.log('   نوع البيانات:', typeof testReturn.date);

    // اختبار إنشاء أمر توريد جديد
    console.log('\n3️⃣ اختبار إنشاء أمر توريد جديد...');
    const testSupplyOrder = await prisma.supplyOrder.create({
      data: {
        supplyOrderId: `TEST-SUP-${timestamp}`,
        supplierName: 'مورد تجريبي',
        supplyDate: new Date(), // استخدام DateTime
        warehouseName: 'المخزن الرئيسي',
        status: 'مكتمل'
      }
    });
    console.log('✅ تم إنشاء أمر التوريد بنجاح، ID:', testSupplyOrder.id);
    console.log('   التاريخ المحفوظ:', testSupplyOrder.supplyDate);
    console.log('   نوع البيانات:', typeof testSupplyOrder.supplyDate);

    // اختبار الاستعلام والترتيب حسب التاريخ
    console.log('\n4️⃣ اختبار الترتيب حسب التاريخ...');
    const salesOrderedByDate = await prisma.sale.findMany({
      orderBy: { date: 'desc' },
      take: 3,
      select: {
        id: true,
        soNumber: true,
        date: true,
        clientName: true
      }
    });
    
    console.log('✅ المبيعات مرتبة حسب التاريخ (أحدث أولاً):');
    salesOrderedByDate.forEach((sale, index) => {
      console.log(`   ${index + 1}. ${sale.soNumber} - ${sale.date.toLocaleDateString('en-US')} - ${sale.clientName}`);
    });

    // اختبار البحث بناءً على نطاق تاريخي
    console.log('\n5️⃣ اختبار البحث بنطاق تاريخي...');
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
    
    const todaysSales = await prisma.sale.findMany({
      where: {
        date: {
          gte: startOfDay,
          lt: endOfDay
        }
      },
      select: {
        soNumber: true,
        date: true,
        clientName: true
      }
    });
    
    console.log(`✅ مبيعات اليوم (${today.toLocaleDateString('en-US')}):`);
    if (todaysSales.length > 0) {
      todaysSales.forEach((sale, index) => {
        console.log(`   ${index + 1}. ${sale.soNumber} - ${sale.date.toLocaleString('en-US')} - ${sale.clientName}`);
      });
    } else {
      console.log('   لا توجد مبيعات لهذا اليوم');
    }

    // تنظيف البيانات التجريبية
    console.log('\n🧹 تنظيف البيانات التجريبية...');
    await prisma.sale.delete({ where: { id: testSale.id } });
    await prisma.return.delete({ where: { id: testReturn.id } });
    await prisma.supplyOrder.delete({ where: { id: testSupplyOrder.id } });
    console.log('✅ تم حذف البيانات التجريبية بنجاح');

    console.log('\n🎉 جميع اختبارات DateTime نجحت!');
    console.log('✅ التواريخ يتم حفظها كـ DateTime objects');
    console.log('✅ الترتيب حسب التاريخ يعمل بشكل صحيح');
    console.log('✅ البحث بالنطاق التاريخي يعمل بشكل صحيح');
    console.log('✅ التنسيق بالإنجليزية يعمل بشكل صحيح');

  } catch (error) {
    console.error('❌ خطأ في اختبار DateTime:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل الاختبار
testDateTimeFields();
