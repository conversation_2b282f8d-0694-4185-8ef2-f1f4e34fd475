/**
 * اختبار نظام المسودات المحدث لصفحة التوريد
 * يختبر حفظ واسترجاع المسودات من قاعدة البيانات الأساسية فقط
 * (تم إزالة localStorage لتجنب تضارب البيانات بين الأجهزة)
 */

const BASE_URL = 'http://localhost:9005';

// بيانات اختبار المسودة
const testDraftData = {
  formState: {
    supplierId: '1',
    warehouseId: '1',
    employeeName: 'مختبر النظام',
    invoiceNumber: 'TEST-DRAFT-001',
    supplyDate: new Date().toISOString().split('T')[0],
    notes: 'اختبار حفظ المسودة في قاعدة البيانات',
    referenceNumber: 'REF-DRAFT-001'
  },
  currentItems: [
    {
      imei: 'DRAFT123456789',
      manufacturer: 'Samsung',
      model: 'Galaxy Test',
      condition: 'جديد'
    },
    {
      imei: 'DRAFT987654321',
      manufacturer: 'Apple',
      model: 'iPhone Test',
      condition: 'جديد'
    }
  ],
  attachments: [
    {
      fileName: 'test-invoice.pdf',
      fileSize: 1024
    }
  ],
  supplyOrderId: 'DRAFT-TEST-001',
  timestamp: new Date().toISOString()
};

async function testSupplyDraftSystem() {
  console.log('🧪 بدء اختبار نظام المسودات المحدث...\n');

  try {
    // الحصول على token للمصادقة
    console.log('1️⃣ تسجيل الدخول...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      })
    });

    if (!loginResponse.ok) {
      throw new Error('فشل في تسجيل الدخول');
    }

    const loginData = await loginResponse.json();
    const authToken = loginData.token;
    console.log('✅ تم تسجيل الدخول بنجاح\n');

    const authHeaders = {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json'
    };

    // اختبار حفظ المسودة
    console.log('2️⃣ اختبار حفظ المسودة في قاعدة البيانات...');
    const saveDraftResponse = await fetch(`${BASE_URL}/api/supply-draft`, {
      method: 'POST',
      headers: authHeaders,
      body: JSON.stringify(testDraftData)
    });

    if (!saveDraftResponse.ok) {
      const errorText = await saveDraftResponse.text();
      throw new Error(`فشل في حفظ المسودة: ${errorText}`);
    }

    const saveResult = await saveDraftResponse.json();
    console.log('✅ تم حفظ المسودة بنجاح:', saveResult.message);
    console.log('📄 معرف المسودة:', saveResult.draft.id);
    console.log('📅 تاريخ الحفظ:', new Date(saveResult.draft.createdAt).toLocaleString('ar-EG'));
    console.log('');

    // اختبار استرجاع المسودات
    console.log('3️⃣ اختبار استرجاع المسودات من قاعدة البيانات...');
    const getDraftsResponse = await fetch(`${BASE_URL}/api/supply-draft`, {
      method: 'GET',
      headers: authHeaders
    });

    if (!getDraftsResponse.ok) {
      throw new Error('فشل في استرجاع المسودات');
    }

    const getDraftsResult = await getDraftsResponse.json();
    console.log('✅ تم استرجاع المسودات بنجاح');
    console.log('📊 عدد المسودات المحفوظة:', getDraftsResult.drafts.length);
    
    if (getDraftsResult.drafts.length > 0) {
      const latestDraft = getDraftsResult.drafts[0];
      console.log('📋 أحدث مسودة:');
      console.log('  - المعرف:', latestDraft.id);
      console.log('  - رقم أمر التوريد:', latestDraft.supplyOrderId);
      console.log('  - عدد الأجهزة:', JSON.parse(latestDraft.currentItems).length);
      console.log('  - تاريخ التحديث:', new Date(latestDraft.updatedAt).toLocaleString('ar-EG'));
    }
    console.log('');

    // اختبار تحديث المسودة
    console.log('4️⃣ اختبار تحديث المسودة...');
    const updatedDraftData = {
      ...testDraftData,
      formState: {
        ...testDraftData.formState,
        notes: 'تم تحديث المسودة - اختبار التحديث'
      },
      currentItems: [
        ...testDraftData.currentItems,
        {
          imei: 'UPDATED123456789',
          manufacturer: 'Xiaomi',
          model: 'Mi Test Updated',
          condition: 'جديد'
        }
      ]
    };

    const updateDraftResponse = await fetch(`${BASE_URL}/api/supply-draft`, {
      method: 'POST',
      headers: authHeaders,
      body: JSON.stringify(updatedDraftData)
    });

    if (!updateDraftResponse.ok) {
      throw new Error('فشل في تحديث المسودة');
    }

    const updateResult = await updateDraftResponse.json();
    console.log('✅ تم تحديث المسودة بنجاح');
    console.log('📝 عدد الأجهزة بعد التحديث:', JSON.parse(updateResult.draft.currentItems).length);
    console.log('');

    // اختبار حذف المسودة
    console.log('5️⃣ اختبار حذف المسودة...');
    const deleteDraftResponse = await fetch(`${BASE_URL}/api/supply-draft`, {
      method: 'DELETE',
      headers: authHeaders
    });

    if (!deleteDraftResponse.ok) {
      throw new Error('فشل في حذف المسودة');
    }

    const deleteResult = await deleteDraftResponse.json();
    console.log('✅ تم حذف المسودة بنجاح:', deleteResult.message);
    console.log('');

    // التحقق من الحذف
    console.log('6️⃣ التحقق من حذف المسودة...');
    const verifyDeleteResponse = await fetch(`${BASE_URL}/api/supply-draft`, {
      method: 'GET',
      headers: authHeaders
    });

    if (verifyDeleteResponse.ok) {
      const verifyResult = await verifyDeleteResponse.json();
      console.log('📊 عدد المسودات بعد الحذف:', verifyResult.drafts.length);
      
      if (verifyResult.drafts.length === 0) {
        console.log('✅ تم التحقق من حذف جميع المسودات بنجاح');
      } else {
        console.log('⚠️  لا تزال هناك مسودات موجودة');
      }
    }

    console.log('\n🎉 تم اكتمال جميع اختبارات نظام المسودات بنجاح!');
    console.log('\n📋 ملخص النتائج:');
    console.log('✅ حفظ المسودة في قاعدة البيانات فقط (بدون localStorage)');
    console.log('✅ استرجاع المسودات من قاعدة البيانات فقط');
    console.log('✅ تحديث المسودة الموجودة في قاعدة البيانات');
    console.log('✅ حذف المسودة من قاعدة البيانات');
    console.log('✅ التحقق من عمليات الحذف');
    console.log('✅ عدم وجود تضارب بيانات بين الأجهزة المختلفة');
    console.log('✅ التفويض يعمل بشكل صحيح مع جميع العمليات');

  } catch (error) {
    console.error('❌ فشل في اختبار نظام المسودات:', error.message);
    console.error('📋 تفاصيل الخطأ:', error);
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testSupplyDraftSystem();
}

module.exports = { testSupplyDraftSystem };
