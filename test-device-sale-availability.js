/**
 * اختبار فحص إمكانية بيع الأجهزة في صفحة المبيعات
 * يحاكي سيناريوهات مختلفة لحالات الأجهزة
 */

console.log('🧪 اختبار فحص إمكانية بيع الأجهزة...\n');

// محاكاة دالة checkDeviceAvailabilityForSale
function checkDeviceAvailabilityForSale(device) {
  // تجميع الحالات حسب نوع المنع
  const maintenanceStatuses = [
    'بانتظار إرسال للصيانة',
    'تحتاج صيانة', 
    'بانتظار استلام في الصيانة',
    'قيد الإصلاح',
    'بانتظار تسليم من الصيانة',
    'بانتظار قطع غيار',
    'مراجعة الطلب من الإدارة'
  ];

  const transitStatuses = [
    'قيد النقل',
    'مرسل للمخزن',
    'بانتظار استلام في المخزن'
  ];

  const unavailableStatuses = [
    'مباع',
    'تالف',
    'معيب',
    'غير قابل للإصلاح'
  ];

  const processingStatuses = [
    'تم التسليم'
  ];

  // فحص الحالة وإرجاع الرسالة المناسبة
  if (maintenanceStatuses.includes(device.status)) {
    const statusMessages = {
      'بانتظار إرسال للصيانة': 'الجهاز مقرر إرساله للصيانة. يجب إلغاء أمر الصيانة أولاً.',
      'تحتاج صيانة': 'الجهاز يحتاج صيانة. يجب إرساله للصيانة أو تغيير حالته.',
      'بانتظار استلام في الصيانة': 'الجهاز في طريقه للصيانة. انتظر وصوله أو ألغِ الأمر.',
      'قيد الإصلاح': 'الجهاز قيد الإصلاح في مركز الصيانة. انتظر انتهاء الإصلاح.',
      'بانتظار تسليم من الصيانة': 'الجهاز جاهز للاستلام من الصيانة. يجب استلامه أولاً.',
      'بانتظار قطع غيار': 'الجهاز ينتظر قطع غيار. لا يمكن بيعه في هذه الحالة.',
      'مراجعة الطلب من الإدارة': 'طلب صيانة الجهاز قيد المراجعة. انتظر موافقة الإدارة.'
    };
    
    return {
      canSell: false,
      reason: statusMessages[device.status] || `الجهاز في الصيانة (${device.status}). لا يمكن بيعه حالياً.`
    };
  }

  if (transitStatuses.includes(device.status)) {
    const statusMessages = {
      'قيد النقل': 'الجهاز قيد النقل بين المخازن. انتظر وصوله للمخزن المحدد.',
      'مرسل للمخزن': 'الجهاز مرسل للمخزن. انتظر وصوله وتحديث الحالة.',
      'بانتظار استلام في المخزن': 'الجهاز ينتظر الاستلام في المخزن. يجب تأكيد الاستلام أولاً.'
    };

    return {
      canSell: false,
      reason: statusMessages[device.status] || `الجهاز في عملية نقل (${device.status}). لا يمكن بيعه حالياً.`
    };
  }

  if (unavailableStatuses.includes(device.status)) {
    const statusMessages = {
      'مباع': 'الجهاز مباع بالفعل. لا يمكن بيعه مرة أخرى.',
      'تالف': 'الجهاز تالف. لا يمكن بيعه في هذه الحالة.',
      'معيب': 'الجهاز معيب. يجب إصلاحه أو تغيير حالته قبل البيع.',
      'غير قابل للإصلاح': 'الجهاز غير قابل للإصلاح. لا يمكن بيعه.'
    };

    return {
      canSell: false,
      reason: statusMessages[device.status] || `الجهاز غير متاح للبيع (${device.status}).`
    };
  }

  if (processingStatuses.includes(device.status)) {
    return {
      canSell: false,
      reason: 'الجهاز تم تسليمه. تحقق من حالة الجهاز وقم بتحديثها إذا لزم الأمر.'
    };
  }

  // إذا كانت الحالة 'متاح للبيع' أو أي حالة أخرى غير محددة
  if (device.status === 'متاح للبيع') {
    return {
      canSell: true,
      reason: 'الجهاز متاح للبيع'
    };
  }

  // حالة احتياطية للحالات غير المعروفة
  return {
    canSell: false,
    reason: `حالة الجهاز غير معروفة (${device.status}). يرجى التحقق من النظام.`
  };
}

// بيانات اختبار تغطي جميع الحالات
const testDevices = [
  // ✅ أجهزة متاحة للبيع
  { id: 'TEST001', model: 'iPhone 13', status: 'متاح للبيع', category: 'متاح' },
  
  // ❌ أجهزة في الصيانة
  { id: 'TEST002', model: 'Samsung S21', status: 'بانتظار إرسال للصيانة', category: 'صيانة' },
  { id: 'TEST003', model: 'iPhone 12', status: 'تحتاج صيانة', category: 'صيانة' },
  { id: 'TEST004', model: 'Huawei P30', status: 'بانتظار استلام في الصيانة', category: 'صيانة' },
  { id: 'TEST005', model: 'Xiaomi Mi 11', status: 'قيد الإصلاح', category: 'صيانة' },
  { id: 'TEST006', model: 'Oppo Reno', status: 'بانتظار تسليم من الصيانة', category: 'صيانة' },
  { id: 'TEST007', model: 'Vivo V20', status: 'بانتظار قطع غيار', category: 'صيانة' },
  { id: 'TEST008', model: 'OnePlus 9', status: 'مراجعة الطلب من الإدارة', category: 'صيانة' },
  
  // ❌ أجهزة في النقل
  { id: 'TEST009', model: 'Google Pixel', status: 'قيد النقل', category: 'نقل' },
  { id: 'TEST010', model: 'Nokia 8', status: 'مرسل للمخزن', category: 'نقل' },
  { id: 'TEST011', model: 'LG G8', status: 'بانتظار استلام في المخزن', category: 'نقل' },
  
  // ❌ أجهزة غير متاحة
  { id: 'TEST012', model: 'iPhone X', status: 'مباع', category: 'غير متاح' },
  { id: 'TEST013', model: 'Samsung Note', status: 'تالف', category: 'غير متاح' },
  { id: 'TEST014', model: 'Huawei Mate', status: 'معيب', category: 'غير متاح' },
  { id: 'TEST015', model: 'Xiaomi Redmi', status: 'غير قابل للإصلاح', category: 'غير متاح' },
  
  // ❌ أجهزة في المعالجة
  { id: 'TEST016', model: 'Sony Xperia', status: 'تم التسليم', category: 'معالجة' },
  
  // ❓ حالة غير معروفة
  { id: 'TEST017', model: 'Unknown Phone', status: 'حالة غريبة', category: 'غير معروف' }
];

// تشغيل الاختبارات
function runDeviceAvailabilityTests() {
  console.log('🚀 بدء اختبار فحص إمكانية بيع الأجهزة\n');
  console.log('=' * 60 + '\n');

  const categories = {
    'متاح': { count: 0, devices: [] },
    'صيانة': { count: 0, devices: [] },
    'نقل': { count: 0, devices: [] },
    'غير متاح': { count: 0, devices: [] },
    'معالجة': { count: 0, devices: [] },
    'غير معروف': { count: 0, devices: [] }
  };

  testDevices.forEach((device, index) => {
    const result = checkDeviceAvailabilityForSale(device);
    const status = result.canSell ? '✅ متاح' : '❌ ممنوع';
    
    console.log(`${index + 1}. ${device.model} (${device.id}):`);
    console.log(`   📱 الحالة: ${device.status}`);
    console.log(`   ${status} ${result.canSell ? 'للبيع' : ''}`);
    console.log(`   💬 الرسالة: ${result.reason}`);
    console.log('');

    // تجميع الإحصائيات
    categories[device.category].count++;
    categories[device.category].devices.push({
      model: device.model,
      status: device.status,
      canSell: result.canSell
    });
  });

  // عرض الإحصائيات
  console.log('=' * 60);
  console.log('📊 إحصائيات النتائج:\n');

  Object.entries(categories).forEach(([category, data]) => {
    if (data.count > 0) {
      const available = data.devices.filter(d => d.canSell).length;
      const unavailable = data.devices.filter(d => !d.canSell).length;
      
      console.log(`📂 ${category}:`);
      console.log(`   📱 إجمالي: ${data.count} جهاز`);
      console.log(`   ✅ متاح للبيع: ${available}`);
      console.log(`   ❌ غير متاح: ${unavailable}`);
      console.log('');
    }
  });

  // تقييم النتائج
  const totalDevices = testDevices.length;
  const availableDevices = testDevices.filter(d => checkDeviceAvailabilityForSale(d).canSell).length;
  const blockedDevices = totalDevices - availableDevices;

  console.log('🎯 ملخص عام:');
  console.log(`📱 إجمالي الأجهزة المختبرة: ${totalDevices}`);
  console.log(`✅ أجهزة متاحة للبيع: ${availableDevices}`);
  console.log(`❌ أجهزة ممنوعة من البيع: ${blockedDevices}`);
  console.log(`📊 نسبة الحماية: ${((blockedDevices / totalDevices) * 100).toFixed(1)}%`);

  console.log('\n💡 الخلاصة:');
  console.log('✅ النظام يحمي من بيع الأجهزة المرتبطة بعمليات أخرى');
  console.log('✅ الرسائل واضحة ومفيدة للمستخدم');
  console.log('✅ يغطي جميع حالات الأجهزة المختلفة');
  console.log('✅ يمنع بيع الأجهزة في الصيانة أو النقل');
}

// اختبار سيناريوهات عملية
function testPracticalScenarios() {
  console.log('\n' + '=' * 60);
  console.log('🌍 اختبار سيناريوهات عملية:\n');

  const scenarios = [
    {
      name: 'محاولة بيع جهاز في الصيانة',
      device: { id: 'MAIN001', model: 'iPhone 13 Pro', status: 'قيد الإصلاح' },
      expected: 'يجب أن يُرفض البيع مع رسالة واضحة'
    },
    {
      name: 'محاولة بيع جهاز قيد النقل',
      device: { id: 'TRANS001', model: 'Samsung Galaxy', status: 'قيد النقل' },
      expected: 'يجب أن يُرفض البيع حتى وصول الجهاز'
    },
    {
      name: 'محاولة بيع جهاز مباع مسبقاً',
      device: { id: 'SOLD001', model: 'Huawei P40', status: 'مباع' },
      expected: 'يجب أن يُرفض البيع منعاً للأخطاء'
    },
    {
      name: 'بيع جهاز متاح',
      device: { id: 'AVAIL001', model: 'Xiaomi Mi 12', status: 'متاح للبيع' },
      expected: 'يجب أن يُقبل البيع بدون مشاكل'
    }
  ];

  scenarios.forEach((scenario, index) => {
    const result = checkDeviceAvailabilityForSale(scenario.device);
    
    console.log(`${index + 1}. ${scenario.name}:`);
    console.log(`   📱 الجهاز: ${scenario.device.model} (${scenario.device.status})`);
    console.log(`   🎯 المتوقع: ${scenario.expected}`);
    console.log(`   📤 النتيجة: ${result.canSell ? '✅ مقبول' : '❌ مرفوض'}`);
    console.log(`   💬 الرسالة: "${result.reason}"`);
    
    const isCorrect = (result.canSell && scenario.device.status === 'متاح للبيع') || 
                     (!result.canSell && scenario.device.status !== 'متاح للبيع');
    
    console.log(`   ${isCorrect ? '✅ النتيجة صحيحة' : '❌ النتيجة خاطئة'}\n`);
  });
}

// تشغيل جميع الاختبارات
runDeviceAvailabilityTests();
testPracticalScenarios();

console.log('\n🎉 انتهاء اختبار فحص إمكانية بيع الأجهزة');
console.log('💡 يمكن الآن اختبار النظام عملياً في صفحة المبيعات');
