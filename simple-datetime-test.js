// اختبار بسيط لوظائف DateTime
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testDateTime() {
  try {
    console.log('🧪 اختبار DateTime بسيط...\n');

    const timestamp = Date.now();

    // اختبار المبيعة
    console.log('1️⃣ اختبار DateTime في المبيعات...');
    const sale = await prisma.sale.create({
      data: {
        soNumber: `TEST-SO-${timestamp}`,
        opNumber: `TEST-OP-${timestamp}`,
        date: new Date(), // DateTime object
        clientName: 'عميل تجريبي',
        warehouseName: 'المخزن الرئيسي',
        employeeName: 'موظف تجريبي',
        warrantyPeriod: 'none'
      }
    });

    console.log('✅ تم إنشاء المبيعة بنجاح');
    console.log(`   التاريخ: ${sale.date}`);
    console.log(`   نوع البيانات: ${typeof sale.date}`);
    console.log(`   التنسيق الإنجليزي: ${sale.date.toLocaleDateString('en-US')}`);
    console.log(`   التنسيق مع الوقت: ${sale.date.toLocaleString('en-US')}`);

    // اختبار الترتيب
    console.log('\n2️⃣ اختبار الترتيب حسب التاريخ...');
    const recentSales = await prisma.sale.findMany({
      orderBy: { date: 'desc' },
      take: 3,
      select: {
        soNumber: true,
        date: true,
        clientName: true
      }
    });

    console.log('✅ المبيعات الأحدث:');
    recentSales.forEach((s, i) => {
      console.log(`   ${i + 1}. ${s.soNumber} - ${s.date.toLocaleDateString('en-US')} - ${s.clientName}`);
    });

    // اختبار البحث بالتاريخ
    console.log('\n3️⃣ اختبار البحث بالتاريخ...');
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    const todaysSales = await prisma.sale.count({
      where: {
        date: {
          gte: startOfDay,
          lt: endOfDay
        }
      }
    });

    console.log(`✅ عدد مبيعات اليوم (${today.toLocaleDateString('en-US')}): ${todaysSales}`);

    // تنظيف
    await prisma.sale.delete({ where: { id: sale.id } });
    console.log('\n🧹 تم حذف البيانات التجريبية');

    console.log('\n🎉 جميع اختبارات DateTime نجحت!');
    console.log('✅ التواريخ محفوظة كـ DateTime objects');
    console.log('✅ التنسيق الإنجليزي يعمل بشكل صحيح');
    console.log('✅ الترتيب والبحث يعملان بشكل صحيح');

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testDateTime();
