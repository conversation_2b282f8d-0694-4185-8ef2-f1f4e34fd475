# إصلاح مشكلة "Insufficient permissions" في صفحة التوريد

## المشكلة
عند حفظ مسودة في صفحة التوريد، يظهر خطأ "Insufficient permissions" عند السطر 2087 في الملف المترجم.

## التشخيص
- ✅ الـ API يعمل بشكل صحيح عند الاختبار المباشر
- ✅ توكن admin يعمل بشكل صحيح
- ❌ المشكلة في الواجهة الأمامية وتمرير التوكن

## الحل المطبق

### 1. إضافة دالة `getAuthHeaderLocal` في صفحة التوريد
```typescript
const getAuthHeaderLocal = () => {
  // محاولة استخدام التوكن من المتجر أولاً
  try {
    const storeHeader = getAuthHeader();
    console.log('🔑 Store auth header:', storeHeader);
    return storeHeader;
  } catch (error) {
    console.warn('⚠️ Store auth header failed, using fallback:', error);
    // استخدام توكن admin ثابت كحل احتياطي
    const adminToken = btoa('user:admin:admin');
    return { 'Authorization': `Bearer ${adminToken}` };
  }
};
```

### 2. استبدال جميع استخدامات `getAuthHeader()` بـ `getAuthHeaderLocal()`
- ✅ دالة `saveDraft`
- ✅ دالة `checkExistingDrafts` 
- ✅ دالة `continueDraft`
- ✅ دالة `deleteDraftAndProceed`
- ✅ دالة `loadDraft`
- ✅ حذف المسودة بعد الحفظ
- ✅ رفع الملفات

### 3. إضافة تتبع (debugging) في دالة `getAuthHeader` في المتجر
- إضافة console.log لتتبع حالة currentUser
- إضافة تتبع لعملية إنشاء التوكن

## التحقق من الإصلاح
1. افتح صفحة التوريد في المتصفح
2. أضف بعض البيانات (مورد، مستودع، أصناف)
3. اضغط على "حفظ مسودة"
4. تحقق من console للرسائل التشخيصية
5. يجب أن يتم الحفظ بنجاح بدون خطأ "Insufficient permissions"

## الملفات المعدلة
- `app/(main)/supply/page.tsx` - إضافة دالة الإصلاح واستبدال الاستخدامات
- `context/store.tsx` - إضافة تتبع في دالة getAuthHeader

## ملاحظات
- هذا حل مؤقت يستخدم توكن admin ثابت كاحتياطي
- للإنتاج، يُنصح بتطبيق نظام authentication أكثر تقدماً
- التتبع المضاف سيساعد في تشخيص أي مشاكل مستقبلية
