const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkAndMigrateUser() {
  try {
    console.log('🔍 التحقق من المستخدمين الذين لديهم صلاحيات JSON...');
    
    const users = await prisma.user.findMany({
      where: {
        permissions: { not: null }
      }
    });

    console.log(`وجد ${users.length} مستخدم لديه صلاحيات JSON`);

    for (const user of users) {
      console.log(`\n👤 المستخدم: ${user.username || user.name}`);
      console.log(`الصلاحيات:`, JSON.stringify(user.permissions, null, 2));
      
      if (user.permissions && typeof user.permissions === 'object') {
        const permissions = user.permissions;
        
        for (const [permName, permData] of Object.entries(permissions)) {
          if (typeof permData === 'object' && permData !== null) {
            console.log(`\n🔧 معالجة صلاحية: ${permName}`);
            console.log(`البيانات:`, JSON.stringify(permData, null, 2));
            
            // البحث عن الصلاحية
            const permissionRecord = await prisma.permission.findUnique({
              where: { name: permName }
            });

            if (permissionRecord) {
              console.log(`✅ وجدت الصلاحية: ${permissionRecord.displayName}`);
              
              try {
                await prisma.userPermission.upsert({
                  where: {
                    userId_permissionId: {
                      userId: user.id,
                      permissionId: permissionRecord.id
                    }
                  },
                  update: {
                    canView: !!permData.view,
                    canCreate: !!permData.create,
                    canEdit: !!(permData.edit || permData.update),
                    canDelete: !!permData.delete,
                    canViewAll: !!permData.viewAll,
                    canManage: !!permData.manage,
                  },
                  create: {
                    userId: user.id,
                    permissionId: permissionRecord.id,
                    canView: !!permData.view,
                    canCreate: !!permData.create,
                    canEdit: !!(permData.edit || permData.update),
                    canDelete: !!permData.delete,
                    canViewAll: !!permData.viewAll,
                    canManage: !!permData.manage,
                  }
                });
                console.log(`✅ تم ترحيل صلاحية ${permName} (عرض: ${!!permData.view}, إنشاء: ${!!permData.create}, تعديل: ${!!(permData.edit || permData.update)}, حذف: ${!!permData.delete})`);
              } catch (error) {
                console.log(`❌ خطأ في ترحيل صلاحية ${permName}:`, error.message);
              }
            } else {
              console.log(`⚠️ صلاحية غير موجودة: ${permName}`);
            }
          }
        }
      }
    }

    // التحقق من النتائج
    const userPermissions = await prisma.userPermission.findMany({
      include: {
        permission: true,
        user: true
      }
    });

    console.log(`\n📊 إجمالي الصلاحيات المرحلة: ${userPermissions.length}`);
    
    for (const up of userPermissions) {
      console.log(`- ${up.user.username}: ${up.permission.displayName} (عرض: ${up.canView}, إنشاء: ${up.canCreate}, تعديل: ${up.canEdit}, حذف: ${up.canDelete})`);
    }

  } catch (error) {
    console.error('خطأ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAndMigrateUser();
