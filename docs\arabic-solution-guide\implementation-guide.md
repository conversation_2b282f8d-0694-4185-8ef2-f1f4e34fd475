# دليل التطبيق العملي لحل مشكلة اللغة العربية

## 🎯 خطوات التطبيق في مشروع جديد

### الخطوة 1: تحديد الطريقة المناسبة

```typescript
// اختر الطريقة حسب احتياجاتك:

// للجودة العالية والتحكم الكامل
const method = 'canvas'; // الأفضل

// للسهولة والسرعة
const method = 'html'; // سهل التطبيق

// للتوافق مع الأنظمة القديمة
const method = 'jspdf'; // متوافق

// للمشاريع الكبيرة
const method = 'unified'; // نظام شامل
```

### الخطوة 2: تثبيت المكتبات المطلوبة

```bash
# للطريقة الأولى (Canvas)
npm install jspdf

# للطريقة الثانية (HTML) - لا تحتاج مكتبات إضافية

# للطريقة الثالثة (jsPDF)
npm install jspdf jspdf-autotable

# للطريقة الرابعة (النظام الموحد)
npm install jspdf jspdf-autotable
```

### الخطوة 3: إنشاء الملفات الأساسية

```
src/
├── lib/
│   └── export-utils/
│       ├── canvas-pdf.ts      # الطريقة الأولى
│       ├── html-print.ts      # الطريقة الثانية
│       ├── jspdf-arabic.ts    # الطريقة الثالثة
│       └── unified-system.ts  # الطريقة الرابعة
├── hooks/
│   └── useArabicPrintExport.ts
└── components/
    └── ui/
        └── arabic-print-buttons.tsx
```

### الخطوة 4: التطبيق في الصفحات

```typescript
// في أي صفحة تريد إضافة الطباعة
import { useArabicPrintExport, ArabicTemplates } from '@/hooks/useArabicPrintExport';

function MyPage({ data }) {
  const { printData, exportToPDF, isLoading } = useArabicPrintExport();
  
  const handlePrint = () => {
    const printData = ArabicTemplates.supply(data);
    printData(printData, { method: 'canvas' });
  };
  
  return (
    <button onClick={handlePrint} disabled={isLoading}>
      {isLoading ? 'جاري الطباعة...' : 'طباعة'}
    </button>
  );
}
```

## 🔧 نصائح للتطبيق الناجح

### 1. اختبار الخطوط
```css
/* تأكد من تحميل الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap');

body {
  font-family: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif !important;
}
```

### 2. معالجة الأخطاء
```typescript
try {
  await printData(data, options);
} catch (error) {
  console.error('خطأ في الطباعة:', error);
  // عرض رسالة خطأ للمستخدم
  showNotification('حدث خطأ أثناء الطباعة', 'error');
}
```

### 3. تحسين الأداء
```typescript
// استخدام الاستيراد الديناميكي
const printModule = await import('./canvas-pdf-enhanced');
await printModule.createArabicPDFWithCanvas(data);
```

## 🎨 تخصيص التصميم

### ألوان عربية مناسبة
```css
:root {
  --arabic-primary: #2c5aa0;    /* أزرق داكن */
  --arabic-secondary: #f8f9fa;  /* رمادي فاتح */
  --arabic-accent: #28a745;     /* أخضر */
  --arabic-text: #212529;       /* نص داكن */
  --arabic-border: #dee2e6;     /* حدود فاتحة */
}
```

### خطوط مُحسنة
```css
.arabic-text {
  font-family: 'Cairo', 'Noto Sans Arabic', 'Amiri', Arial, sans-serif !important;
  direction: rtl;
  text-align: right;
  line-height: 1.6;
}
```

## 🐛 حل المشاكل الشائعة

### مشكلة: أحرف غريبة
```typescript
// الحل: استخدم Canvas أو HTML بدلاً من jsPDF المباشر
const method = 'canvas'; // بدلاً من 'jspdf'
```

### مشكلة: اتجاه خاطئ
```css
/* تأكد من إعدادات RTL */
html { direction: rtl; }
body { direction: rtl; text-align: right; }
```

### مشكلة: خطوط لا تظهر
```typescript
// انتظار تحميل الخطوط
document.fonts.ready.then(() => {
  // بدء الطباعة هنا
  printData(data);
});
```

### مشكلة: النوافذ المنبثقة محجوبة
```typescript
// استخدم Canvas للتصدير المباشر
const method = 'canvas'; // ينزل الملف مباشرة
```

## 📊 مقارنة الطرق

| الطريقة | الجودة | السهولة | الحجم | التوافق | الأفضل لـ |
|---------|--------|---------|-------|---------|-----------|
| Canvas | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐ | التقارير المعقدة |
| HTML | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | الطباعة السريعة |
| jsPDF | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | الأنظمة القديمة |
| موحد | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | المشاريع الكبيرة |

## 🚀 أمثلة سريعة للتطبيق

### للتوريد
```typescript
const supplyData = ArabicTemplates.supply({
  id: 'PO-001',
  supplierName: 'شركة التقنية',
  date: '2024-01-15',
  items: [...]
});

printData(supplyData, { method: 'canvas' });
```

### للمبيعات
```typescript
const salesData = ArabicTemplates.sales({
  number: 'INV-001',
  customerName: 'أحمد محمد',
  date: '2024-01-15',
  items: [...]
});

exportToPDF(salesData, { fileName: 'invoice_001' });
```

### للمخزون
```typescript
const inventoryData = ArabicTemplates.inventory({
  warehouseName: 'المخزن الرئيسي',
  date: '2024-01-15',
  totalItems: 150,
  items: [...]
});

printData(inventoryData, { method: 'html' });
```

## 📝 قائمة مراجعة التطبيق

- [ ] تثبيت المكتبات المطلوبة
- [ ] إنشاء ملفات الحلول
- [ ] اختبار الخطوط العربية
- [ ] تطبيق في صفحة واحدة
- [ ] اختبار جميع الطرق
- [ ] معالجة الأخطاء
- [ ] تحسين الأداء
- [ ] اختبار على متصفحات مختلفة
- [ ] اختبار الطباعة الفعلية
- [ ] توثيق الاستخدام

## 🎯 النصيحة الذهبية

**ابدأ بالطريقة الثانية (HTML)** للتطبيق السريع، ثم انتقل للطريقة الأولى (Canvas) للحصول على أفضل جودة، وأخيراً استخدم النظام الموحد للمشاريع الكبيرة.

**الطريقة الأفضل = Canvas + HTML كبديل + معالجة شاملة للأخطاء**
