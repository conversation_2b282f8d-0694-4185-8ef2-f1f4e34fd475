# توصيات تحسين بنية قاعدة البيانات - استبدال حقول JSON

## المشكلة الحالية

استخدام حقول JSON في عدة نماذج يؤدي إلى:
- صعوبة في الاستعلام والفهرسة
- فقدان التحقق من صحة البيانات على مستوى قاعدة البيانات
- صعوبة في إنشاء علاقات وقيود مرجعية
- مشاكل في الأداء عند البحث في البيانات المعقدة

## التحسينات المقترحة

### 1. نموذج الصلاحيات (User.permissions)

#### الحالة الحالية:
```prisma
model User {
  permissions Json? // للصلاحيات المخزنة كـ JSON
}
```

#### التحسين المقترح:
```prisma
model User {
  id              Int      @id @default(autoincrement())
  // ... الحقول الأخرى
  userPermissions UserPermission[]
}

model Permission {
  id          Int      @id @default(autoincrement())
  name        String   @unique // dashboard, sales, inventory, etc.
  displayName String   // الاسم المعروض
  category    String   // الفئة
  createdAt   DateTime @default(now())
  
  userPermissions UserPermission[]
}

model UserPermission {
  id           Int        @id @default(autoincrement())
  userId       Int
  permissionId Int
  canView      Boolean    @default(false)
  canCreate    Boolean    @default(false)
  canEdit      Boolean    @default(false)
  canDelete    Boolean    @default(false)
  canViewAll   Boolean    @default(false)
  createdAt    DateTime   @default(now())
  
  user         User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  
  @@unique([userId, permissionId])
}
```

### 2. صلاحيات الوصول للمخازن (User.warehouseAccess)

#### الحالة الحالية:
```prisma
model User {
  warehouseAccess Json? // صلاحيات الوصول للمخازن كـ JSON array
}
```

#### التحسين المقترح:
```prisma
model UserWarehouseAccess {
  id          Int       @id @default(autoincrement())
  userId      Int
  warehouseId Int
  accessType  String    @default("read") // read, write, admin
  createdAt   DateTime  @default(now())
  
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  warehouse   Warehouse @relation(fields: [warehouseId], references: [id], onDelete: Cascade)
  
  @@unique([userId, warehouseId])
}

model User {
  warehouseAccess UserWarehouseAccess[]
}

model Warehouse {
  userAccess UserWarehouseAccess[]
}
```

### 3. معلومات الاستبدال (Device.replacementInfo)

#### الحالة الحالية:
```prisma
model Device {
  replacementInfo Json? // معلومات الاستبدال كـ JSON
}
```

#### التحسين المقترح:
```prisma
model Device {
  id                String            @id
  // ... الحقول الأخرى
  replacements      DeviceReplacement[]
  replacedBy        DeviceReplacement[] @relation("ReplacedDevice")
}

model DeviceReplacement {
  id                Int      @id @default(autoincrement())
  originalDeviceId  String
  replacementDeviceId String
  reason           String
  replacementDate  DateTime @default(now())
  notes            String?
  status           String   @default("active") // active, reverted
  
  originalDevice    Device   @relation(fields: [originalDeviceId], references: [id])
  replacementDevice Device   @relation("ReplacedDevice", fields: [replacementDeviceId], references: [id])
  
  @@unique([originalDeviceId, replacementDeviceId])
}
```

### 4. معرفات المستقبلين (InternalMessage.recipientIds)

#### الحالة الحالية:
```prisma
model InternalMessage {
  recipientIds Json? // قائمة معرفات المستقبلين للمجموعات
}
```

#### التحسين المقترح:
```prisma
model InternalMessage {
  id              Int                    @id @default(autoincrement())
  // ... الحقول الأخرى
  recipients      MessageRecipient[]
}

model MessageRecipient {
  id        Int             @id @default(autoincrement())
  messageId Int
  userId    Int
  isRead    Boolean         @default(false)
  readAt    DateTime?
  
  message   InternalMessage @relation(fields: [messageId], references: [id], onDelete: Cascade)
  user      User            @relation(fields: [userId], references: [id])
  
  @@unique([messageId, userId])
}
```

### 5. عناصر أوامر الصيانة (MaintenanceOrder.items)

#### الحالة الحالية:
```prisma
model MaintenanceOrder {
  items String? // JSON string of items
}
```

#### التحسين المقترح:
```prisma
// النموذج موجود بالفعل ولكن يحتاج لاستخدام العلاقة بدلاً من JSON
model MaintenanceOrder {
  // إزالة الحقل items واستخدام العلاقة
  // items String? // إزالة هذا الحقل
  
  itemsRelation MaintenanceOrderItem[] @relation("MaintenanceOrderItems")
}
```

## مزايا التحسينات المقترحة

### 1. أداء أفضل
- إمكانية إنشاء فهارس على الحقول المفردة
- استعلامات أسرع وأكثر كفاءة
- استخدام JOIN بدلاً من JSON parsing

### 2. تحقق من صحة البيانات
- قيود مرجعية (Foreign Key Constraints)
- التحقق من صحة البيانات على مستوى قاعدة البيانات
- منع البيانات التالفة أو غير المتسقة

### 3. مرونة في الاستعلام
- إمكانية البحث المتقدم
- فلترة وترتيب أفضل
- تجميع البيانات بسهولة

### 4. قابلية الصيانة
- بنية واضحة ومفهومة
- سهولة التطوير والتعديل
- توثيق أفضل للعلاقات

## خطة التنفيذ

### المرحلة 1: إنشاء النماذج الجديدة
1. إضافة النماذج الجديدة لقاعدة البيانات
2. تشغيل migration لإنشاء الجداول

### المرحلة 2: ترحيل البيانات
1. كتابة scripts لترحيل البيانات من JSON إلى الجداول الجديدة
2. التحقق من صحة البيانات المهاجرة

### المرحلة 3: تحديث الكود
1. تحديث API endpoints لاستخدام النماذج الجديدة
2. تحديث Frontend components
3. تحديث وظائف المصادقة والتخويل

### المرحلة 4: إزالة الحقول القديمة
1. إزالة حقول JSON من النماذج
2. تنظيف الكود من الاستخدامات القديمة

## مثال على Migration Script

```typescript
// migrate-permissions.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function migrateUserPermissions() {
  const users = await prisma.user.findMany({
    where: {
      permissions: { not: null }
    }
  });

  for (const user of users) {
    if (user.permissions && typeof user.permissions === 'object') {
      const permissions = user.permissions as any;
      
      for (const [permName, permData] of Object.entries(permissions)) {
        if (typeof permData === 'object' && permData !== null) {
          const permissionRecord = await prisma.permission.upsert({
            where: { name: permName },
            update: {},
            create: {
              name: permName,
              displayName: permName,
              category: 'general'
            }
          });

          await prisma.userPermission.upsert({
            where: {
              userId_permissionId: {
                userId: user.id,
                permissionId: permissionRecord.id
              }
            },
            update: {
              canView: (permData as any).view || false,
              canCreate: (permData as any).create || false,
              canEdit: (permData as any).edit || false,
              canDelete: (permData as any).delete || false,
              canViewAll: (permData as any).viewAll || false,
            },
            create: {
              userId: user.id,
              permissionId: permissionRecord.id,
              canView: (permData as any).view || false,
              canCreate: (permData as any).create || false,
              canEdit: (permData as any).edit || false,
              canDelete: (permData as any).delete || false,
              canViewAll: (permData as any).viewAll || false,
            }
          });
        }
      }
    }
  }
}
```

## الخلاصة

استبدال حقول JSON بنماذج علائقية منفصلة سيحسن من:
- الأداء والاستعلامات
- صحة البيانات وتكاملها
- قابلية الصيانة والتطوير
- مرونة النظام وقابليته للتوسع

التنفيذ يجب أن يتم على مراحل لضمان عدم تعطيل النظام الحالي.
