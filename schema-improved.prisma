// نموذج Prisma محسن مع استبدال حقول JSON بنماذج علائقية
// هذا مثال على التحسينات المقترحة

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ===== نماذج الصلاحيات المحسنة =====

model Permission {
  id          Int      @id @default(autoincrement())
  name        String   @unique // dashboard, sales, inventory, etc.
  displayName String   // الاسم المعروض
  category    String   // الفئة: core, sales, maintenance, etc.
  description String?  // وصف الصلاحية
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  userPermissions UserPermission[]
  
  @@map("permissions")
}

model UserPermission {
  id           Int        @id @default(autoincrement())
  userId       Int
  permissionId Int
  canView      Bo<PERSON>an    @default(false)
  canCreate    Boolean    @default(false)
  canEdit      <PERSON>    @default(false)
  canDelete    Boolean    @default(false)
  canViewAll   Boolean    @default(false)
  canManage    Boolean    @default(false)
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  
  user         User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)
  
  @@unique([userId, permissionId])
  @@map("user_permissions")
}

model UserWarehouseAccess {
  id          Int       @id @default(autoincrement())
  userId      Int
  warehouseId Int
  accessType  String    @default("read") // read, write, admin
  canTransfer Boolean   @default(false)
  canAudit    Boolean   @default(false)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  warehouse   Warehouse @relation(fields: [warehouseId], references: [id], onDelete: Cascade)
  
  @@unique([userId, warehouseId])
  @@map("user_warehouse_access")
}

// ===== نموذج المستخدم المحسن =====

model User {
  id              Int      @id @default(autoincrement())
  email           String   @unique
  name            String?
  username        String?  @unique @default("user")
  role            String?  @default("user")
  phone           String?  @default("")
  photo           String?  @default("")
  status          String?  @default("Active")
  lastLogin       DateTime?
  branchLocation  String?  // موقع الفرع
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt @default(now())
  
  // العلاقات المحسنة
  posts               Post[]
  createdDatabases    Database[]
  userPermissions     UserPermission[]
  warehouseAccess     UserWarehouseAccess[]
  messageRecipients   MessageRecipient[]

  @@map("users")
}

// ===== نماذج الاستبدال المحسنة =====

model DeviceReplacement {
  id                  Int      @id @default(autoincrement())
  originalDeviceId    String
  replacementDeviceId String
  reason              String
  replacementDate     DateTime @default(now())
  notes               String?
  status              String   @default("active") // active, reverted
  processedBy         String?  // الموظف الذي قام بالاستبدال
  approvedBy          String?  // الموظف الذي وافق على الاستبدال
  
  originalDevice      Device   @relation("OriginalDevice", fields: [originalDeviceId], references: [id])
  replacementDevice   Device   @relation("ReplacementDevice", fields: [replacementDeviceId], references: [id])
  
  @@unique([originalDeviceId, replacementDeviceId])
  @@map("device_replacements")
}

model Device {
  id          String   @id
  model       String
  status      String
  storage     String
  price       Float
  condition   String
  warehouseId Int?
  supplierId  Int?
  dateAdded   DateTime @default(now())
  
  // العلاقات المحسنة للاستبدال
  originalReplacements    DeviceReplacement[] @relation("OriginalDevice")
  replacementFor         DeviceReplacement[] @relation("ReplacementDevice")
}

// ===== نماذج الرسائل المحسنة =====

model MessageRecipient {
  id        Int             @id @default(autoincrement())
  messageId Int
  userId    Int
  isRead    Boolean         @default(false)
  readAt    DateTime?
  
  message   InternalMessage @relation(fields: [messageId], references: [id], onDelete: Cascade)
  user      User            @relation(fields: [userId], references: [id])
  
  @@unique([messageId, userId])
  @@map("message_recipients")
}

model InternalMessage {
  id                  Int       @id @default(autoincrement())
  threadId            Int       // معرف المحادثة
  senderId            Int       // معرف المرسل
  senderName          String    // اسم المرسل
  recipientId         Int?      // معرف المستقبل الرئيسي (اختياري للمجموعات)
  recipientName       String?   // اسم المستقبل الرئيسي
  text                String    // نص الرسالة
  attachmentName      String?   // اسم المرفق
  attachmentContent   String?   // محتوى المرفق Base64 (للصور الصغيرة)
  attachmentType      String?   // نوع المرفق
  attachmentUrl       String?   // رابط المرفق
  attachmentFileName  String?   // اسم الملف المحفوظ
  attachmentSize      Int?      // حجم المرفق
  sentDate            DateTime  // تاريخ الإرسال
  status              String    @default("مرسلة") // مرسلة، مقروءة، تم الرد، تم الحل
  isRead              Boolean   @default(false)
  parentMessageId     Int?      // معرف الرسالة الأصلية للردود
  employeeRequestId   Int?      // ربط بطلب موظف
  resolutionNote      String?   // ملاحظة الحل
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt @default(now())

  // العلاقات المحسنة
  recipients          MessageRecipient[]

  @@map("internal_messages")
}

// ===== باقي النماذج مع التحسينات =====

model Warehouse {
  id        Int      @id @default(autoincrement())
  name      String
  type      String
  location  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt @default(now())
  
  // العلاقات المحسنة
  userAccess UserWarehouseAccess[]
}

// ===== أمثلة على النماذج الأخرى =====

model Post {
  id        Int     @id @default(autoincrement())
  title     String
  content   String?
  published Boolean @default(false)
  author    User    @relation(fields: [authorId], references: [id])
  authorId  Int
}

model SystemSetting {
  id              Int      @id @default(1)
  logoUrl         String   @default("")
  companyNameAr   String   @default("")
  companyNameEn   String   @default("")
  addressAr       String   @default("")
  addressEn       String   @default("")
  phone           String   @default("")
  email           String   @default("")
  website         String   @default("")
  footerTextAr    String   @default("")
  footerTextEn    String   @default("")
  updatedAt       DateTime @updatedAt @default(now())
  createdAt       DateTime @default(now())
}

// يمكن إضافة باقي النماذج هنا مع التحسينات المناسبة...

// ===== نماذج قواعد البيانات =====

model DatabaseConnection {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  host        String
  port        Int      @default(5432)
  database    String
  username    String
  password    String   // مشفر
  isActive    Boolean  @default(false)
  isDefault   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  backups     DatabaseBackup[]
  databases   Database[]
  
  @@map("database_connections")
}

model DatabaseBackup {
  id           Int                @id @default(autoincrement())
  name         String
  description  String?
  filePath     String
  fileSize     String
  backupType   String             @default("manual") // manual, automatic
  status       String             @default("completed") // pending, completed, failed
  createdBy    String?
  createdAt    DateTime           @default(now())
  
  connection   DatabaseConnection @relation(fields: [connectionId], references: [id], onDelete: Cascade)
  connectionId Int
  
  @@map("database_backups")
}

model Database {
  id           Int                @id @default(autoincrement())
  name         String
  connectionId Int
  owner        String             @default("")
  template     String             @default("template0")
  encoding     String             @default("UTF8")
  createdBy    Int
  createdAt    DateTime           @default(now())
  updatedAt    DateTime           @updatedAt @default(now())
  
  // العلاقات
  connection   DatabaseConnection @relation(fields: [connectionId], references: [id], onDelete: Cascade)
  creator      User               @relation(fields: [createdBy], references: [id])
  
  @@unique([name, connectionId])
  @@map("databases")
}
