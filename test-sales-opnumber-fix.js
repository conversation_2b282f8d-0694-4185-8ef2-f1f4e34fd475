/**
 * اختبار إصلاح مشكلة حفظ رقم الأمر في صفحة المبيعات
 */

console.log('🧪 اختبار إصلاح رقم الأمر في المبيعات...\n');

// اختبار إنشاء فاتورة بيع برقم أمر محدد
async function testCreateSaleWithSpecificOpNumber() {
  try {
    const saleData = {
      opNumber: '1', // رقم الأمر المطلوب
      date: new Date().toISOString(),
      clientName: 'عميل اختبار',
      warehouseName: 'المخزن الرئيسي',
      notes: 'اختبار حفظ رقم الأمر',
      warrantyPeriod: 'none',
      employeeName: 'مدير النظام',
      items: [
        {
          deviceId: 'TEST123456789',
          model: 'iPhone Test',
          price: 1000,
          condition: 'جديد'
        }
      ]
    };

    console.log('📤 إرسال طلب إنشاء فاتورة بيع برقم أمر:', saleData.opNumber);
    console.log('📊 البيانات:', JSON.stringify(saleData, null, 2));

    const response = await fetch('http://localhost:9005/api/sales', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(saleData)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ تم إنشاء فاتورة البيع بنجاح!');
      console.log('📊 النتيجة:', result);
      console.log('🔍 رقم الأمر المحفوظ:', result.opNumber);
      console.log('🔍 رقم الفاتورة SO:', result.soNumber);
      
      if (result.opNumber === saleData.opNumber) {
        console.log('🎯 ممتاز! تم الحفظ بنفس رقم الأمر المطلوب');
        return result.id; // إرجاع ID للاختبار التالي
      } else {
        console.log('❌ مشكلة: تم الحفظ برقم أمر مختلف');
        console.log(`المطلوب: ${saleData.opNumber}`);
        console.log(`المحفوظ: ${result.opNumber}`);
        return null;
      }
    } else {
      console.log('❌ فشل في إنشاء فاتورة البيع');
      console.log('🔍 السبب:', result);
      return null;
    }

  } catch (error) {
    console.error('💥 خطأ في الطلب:', error.message);
    return null;
  }
}

// اختبار تحديث فاتورة موجودة مع تغيير رقم الأمر
async function testUpdateSaleOpNumber(saleId) {
  if (!saleId) {
    console.log('⏭️ تخطي اختبار التحديث لعدم وجود فاتورة صالحة');
    return;
  }

  try {
    const updatedData = {
      id: saleId,
      opNumber: '2', // رقم أمر جديد
      date: new Date().toISOString(),
      clientName: 'عميل اختبار محدث',
      warehouseName: 'المخزن الرئيسي',
      notes: 'اختبار تحديث رقم الأمر',
      warrantyPeriod: '1m',
      employeeName: 'مدير النظام',
      items: [
        {
          deviceId: 'TEST123456789',
          model: 'iPhone Test Updated',
          price: 1200,
          condition: 'جديد'
        }
      ]
    };

    console.log('\n📤 إرسال طلب تحديث فاتورة البيع...');
    console.log('🔍 رقم الأمر الجديد:', updatedData.opNumber);

    const response = await fetch('http://localhost:9005/api/sales', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updatedData)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ تم تحديث فاتورة البيع بنجاح!');
      console.log('🔍 رقم الأمر المحفوظ:', result.opNumber);
      
      if (result.opNumber === updatedData.opNumber) {
        console.log('🎯 ممتاز! تم تحديث رقم الأمر بنجاح');
      } else {
        console.log('❌ مشكلة: لم يتم تحديث رقم الأمر');
        console.log(`المطلوب: ${updatedData.opNumber}`);
        console.log(`المحفوظ: ${result.opNumber}`);
      }
    } else {
      console.log('❌ فشل في تحديث فاتورة البيع');
      console.log('🔍 السبب:', result);
    }

  } catch (error) {
    console.error('💥 خطأ في التحديث:', error.message);
  }
}

// اختبار إنشاء فاتورة بدون رقم أمر (يجب أن يستخدم SO number)
async function testCreateSaleWithoutOpNumber() {
  try {
    const saleData = {
      // لا يوجد opNumber
      date: new Date().toISOString(),
      clientName: 'عميل بدون رقم أمر',
      warehouseName: 'المخزن الرئيسي',
      notes: 'اختبار بدون رقم أمر',
      warrantyPeriod: 'none',
      employeeName: 'مدير النظام',
      items: [
        {
          deviceId: 'TEST987654321',
          model: 'Samsung Test',
          price: 800,
          condition: 'مستعمل'
        }
      ]
    };

    console.log('\n📤 إرسال طلب إنشاء فاتورة بيع بدون رقم أمر...');

    const response = await fetch('http://localhost:9005/api/sales', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(saleData)
    });

    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ تم إنشاء فاتورة البيع بنجاح!');
      console.log('🔍 رقم الأمر المولد تلقائياً:', result.opNumber);
      console.log('🔍 رقم الفاتورة SO:', result.soNumber);
      
      if (result.opNumber === result.soNumber) {
        console.log('🎯 ممتاز! تم استخدام رقم SO كرقم أمر افتراضي');
      } else {
        console.log('⚠️ ملاحظة: رقم الأمر مختلف عن رقم SO');
      }
    } else {
      console.log('❌ فشل في إنشاء فاتورة البيع');
      console.log('🔍 السبب:', result);
    }

  } catch (error) {
    console.error('💥 خطأ في الطلب:', error.message);
  }
}

// تشغيل الاختبارات
async function runTests() {
  console.log('🚀 بدء اختبارات رقم الأمر في المبيعات\n');
  
  // الاختبار الأول: إنشاء فاتورة برقم أمر محدد
  const saleId = await testCreateSaleWithSpecificOpNumber();
  
  // الاختبار الثاني: تحديث رقم الأمر
  await testUpdateSaleOpNumber(saleId);
  
  // الاختبار الثالث: إنشاء فاتورة بدون رقم أمر
  await testCreateSaleWithoutOpNumber();
  
  console.log('\n🎯 انتهاء اختبارات رقم الأمر في المبيعات');
  console.log('💡 إذا ظهرت رسائل نجاح، فقد تم إصلاح المشكلة بنجاح');
}

// تشغيل الاختبارات
runTests();
