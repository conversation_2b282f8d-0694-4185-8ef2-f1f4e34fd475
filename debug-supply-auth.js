/**
 * أداة تشخيص مشكلة الصلاحيات في صفحة التوريد
 */

const BASE_URL = 'http://localhost:9005';

function createAuthToken(username = 'admin', role = 'admin') {
  const tokenData = `user:${username}:${role}`;
  return btoa(tokenData);
}

async function debugSupplyAuth() {
  console.log('🔍 تشخيص مشكلة الصلاحيات في صفحة التوريد...\n');

  try {
    // اختبار الـ API مباشرة
    const authToken = createAuthToken();
    console.log('🔑 Created token:', authToken);
    
    const authHeaders = {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json'
    };

    // محاولة حفظ مسودة بنفس البيانات التي قد ترسلها الواجهة
    const testDraftData = {
      formState: {
        supplierId: '1',
        warehouseId: '1',
        employeeName: 'Admin User',
        invoiceNumber: 'SUP-001',
        supplyDate: new Date().toISOString().split('T')[0],
        notes: 'اختبار حفظ المسودة'
      },
      currentItems: [],
      attachments: [],
      supplyOrderId: 'SUP-DRAFT-001',
      timestamp: new Date().toISOString()
    };

    console.log('📊 Test data:', JSON.stringify(testDraftData, null, 2));

    const response = await fetch(`${BASE_URL}/api/supply-draft`, {
      method: 'POST',
      headers: authHeaders,
      body: JSON.stringify(testDraftData)
    });

    console.log('\n📡 Response status:', response.status);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

    if (response.ok) {
      const result = await response.json();
      console.log('✅ Success:', result);
    } else {
      const errorText = await response.text();
      console.log('❌ Error response:', errorText);
      
      try {
        const errorJson = JSON.parse(errorText);
        console.log('❌ Parsed error:', errorJson);
      } catch (e) {
        console.log('❌ Plain error text:', errorText);
      }
    }

    // اختبار مع users مختلفين
    console.log('\n🧪 اختبار مع مستخدمين مختلفين...');
    
    const users = [
      { username: 'user', role: 'user' },
      { username: 'manager', role: 'manager' },
      { username: 'admin', role: 'admin' }
    ];

    for (const user of users) {
      const userToken = createAuthToken(user.username, user.role);
      const userHeaders = {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json'
      };

      const userResponse = await fetch(`${BASE_URL}/api/supply-draft`, {
        method: 'GET',
        headers: userHeaders
      });

      console.log(`👤 ${user.username} (${user.role}): ${userResponse.status} ${userResponse.statusText}`);
      
      if (!userResponse.ok) {
        const errorText = await userResponse.text();
        console.log(`   ❌ Error: ${errorText}`);
      }
    }

  } catch (error) {
    console.error('❌ Diagnostic failed:', error);
  }
}

// تشغيل التشخيص
if (require.main === module) {
  debugSupplyAuth();
}

module.exports = { debugSupplyAuth };
