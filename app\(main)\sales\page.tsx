'use client';

import React, { useState, useMemo, useRef, useEffect } from 'react';
import { useStore } from '@/context/store';
import { useToast } from '@/hooks/use-toast';
import { DarkModeToggle } from './DarkModeToggle';
import './enhanced-styles.css';
import type {
  Sale,
  SaleItem,
  EmployeeRequestType,
  EmployeeRequestPriority,
  SystemSettings,
  DeviceStatus,
} from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
  SelectLabel,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import AttachmentsViewer from '@/components/AttachmentsViewer';
import {
  PlusCircle,
  Trash2,
  Save,
  Printer,
  FileDown,
  X,
  Upload,
  Check,
  ChevronsUpDown,
  FileSpreadsheet,
  FolderOpen,
  Trash,
  MessageSquareQuote,
  Eye,
  File,
  FileText,
  Download,
} from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { cn } from '@/lib/utils';
import Barcode from 'react-barcode';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

const initialFormState = {
  opNumber: '',
  date: new Date().toISOString().slice(0, 16),
  clientId: '',
  warehouseId: '',
  notes: '',
  warrantyPeriod: 'none',
};

const warrantyMap: { [key: string]: string } = {
  none: 'بدون ضمان',
  '3d': '3 أيام',
  '1w': 'أسبوع',
  '1m': 'شهر واحد',
  '3m': '3 أشهر',
  '6m': '6 أشهر',
  '1y': 'سنة واحدة',
};

const initialRequestFormState = {
  requestType: 'تعديل' as EmployeeRequestType,
  priority: 'عادي' as EmployeeRequestPriority,
  notes: '',
  attachmentName: '',
};

// دالة فحص إمكانية بيع الجهاز
const checkDeviceAvailabilityForSale = (device: any) => {
  // تجميع الحالات حسب نوع المنع
  const maintenanceStatuses: DeviceStatus[] = [
    'بانتظار إرسال للصيانة',
    'تحتاج صيانة', 
    'بانتظار استلام في الصيانة',
    'قيد الإصلاح',
    'بانتظار تسليم من الصيانة',
    'بانتظار قطع غيار',
    'مراجعة الطلب من الإدارة'
  ];

  const transitStatuses: DeviceStatus[] = [
    'قيد النقل',
    'مرسل للمخزن',
    'بانتظار استلام في المخزن'
  ];

  const unavailableStatuses: DeviceStatus[] = [
    'مباع',
    'تالف',
    'معيب',
    'غير قابل للإصلاح'
  ];

  const processingStatuses: DeviceStatus[] = [
    'تم التسليم'
  ];

  // فحص الحالة وإرجاع الرسالة المناسبة
  if (maintenanceStatuses.includes(device.status)) {
    const statusMessages: Record<string, string> = {
      'بانتظار إرسال للصيانة': 'الجهاز مقرر إرساله للصيانة. يجب إلغاء أمر الصيانة أولاً.',
      'تحتاج صيانة': 'الجهاز يحتاج صيانة. يجب إرساله للصيانة أو تغيير حالته.',
      'بانتظار استلام في الصيانة': 'الجهاز في طريقه للصيانة. انتظر وصوله أو ألغِ الأمر.',
      'قيد الإصلاح': 'الجهاز قيد الإصلاح في مركز الصيانة. انتظر انتهاء الإصلاح.',
      'بانتظار تسليم من الصيانة': 'الجهاز جاهز للاستلام من الصيانة. يجب استلامه أولاً.',
      'بانتظار قطع غيار': 'الجهاز ينتظر قطع غيار. لا يمكن بيعه في هذه الحالة.',
      'مراجعة الطلب من الإدارة': 'طلب صيانة الجهاز قيد المراجعة. انتظر موافقة الإدارة.'
    };
    
    return {
      canSell: false,
      reason: statusMessages[device.status] || `الجهاز في الصيانة (${device.status}). لا يمكن بيعه حالياً.`
    };
  }

  if (transitStatuses.includes(device.status)) {
    const statusMessages: Record<string, string> = {
      'قيد النقل': 'الجهاز قيد النقل بين المخازن. انتظر وصوله للمخزن المحدد.',
      'مرسل للمخزن': 'الجهاز مرسل للمخزن. انتظر وصوله وتحديث الحالة.',
      'بانتظار استلام في المخزن': 'الجهاز ينتظر الاستلام في المخزن. يجب تأكيد الاستلام أولاً.'
    };

    return {
      canSell: false,
      reason: statusMessages[device.status] || `الجهاز في عملية نقل (${device.status}). لا يمكن بيعه حالياً.`
    };
  }

  if (unavailableStatuses.includes(device.status)) {
    const statusMessages: Record<string, string> = {
      'مباع': 'الجهاز مباع بالفعل. لا يمكن بيعه مرة أخرى.',
      'تالف': 'الجهاز تالف. لا يمكن بيعه في هذه الحالة.',
      'معيب': 'الجهاز معيب. يجب إصلاحه أو تغيير حالته قبل البيع.',
      'غير قابل للإصلاح': 'الجهاز غير قابل للإصلاح. لا يمكن بيعه.'
    };

    return {
      canSell: false,
      reason: statusMessages[device.status] || `الجهاز غير متاح للبيع (${device.status}).`
    };
  }

  if (processingStatuses.includes(device.status)) {
    return {
      canSell: false,
      reason: 'الجهاز تم تسليمه. تحقق من حالة الجهاز وقم بتحديثها إذا لزم الأمر.'
    };
  }

  // إذا كانت الحالة 'متاح للبيع' أو أي حالة أخرى غير محددة
  if (device.status === 'متاح للبيع') {
    return {
      canSell: true,
      reason: 'الجهاز متاح للبيع'
    };
  }

  // حالة احتياطية للحالات غير المعروفة
  return {
    canSell: false,
    reason: `حالة الجهاز غير معروفة (${device.status}). يرجى التحقق من النظام.`
  };
};

export default function SalesPage() {
  const {
    devices,
    clients,
    warehouses,
    sales,
    addSale,
    updateSale,
    deleteSale,
    checkSaleRelations,
    checkDeviceRelations,
    addContact,
    addEmployeeRequest,
    systemSettings,
    currentUser,
    getAuthHeader, // ✅ إضافة دالة التفويض
  } = useStore();
  const { toast } = useToast();

  // فحص الصلاحيات
  const userPermissions = currentUser?.permissions?.sales;
  const canCreate = userPermissions?.create ?? false;
  const canEdit = userPermissions?.edit ?? false;
  const canDelete = userPermissions?.delete ?? false;
  const canView = userPermissions?.view ?? false;

  // وضع الإنشاء - يتم تفعيله عند بدء إنشاء فاتورة جديدة
  const [isCreateMode, setIsCreateMode] = useState(false);

  const [soNumber, setSoNumber] = useState('');
  const [formState, setFormState] = useState(initialFormState);
  const [invoiceItems, setInvoiceItems] = useState<SaleItem[]>([]);
  const [serialNumber, setSerialNumber] = useState('');
  const [isClientModalOpen, setIsClientModalOpen] = useState(false);
  const [newClientName, setNewClientName] = useState('');
  const [isCancelAlertOpen, setIsCancelAlertOpen] = useState(false);
  const [isClientSearchOpen, setIsClientSearchOpen] = useState(false);
  const [loadedSale, setLoadedSale] = useState<Sale | null>(null);
  const [isViewSalesDialogOpen, setIsViewSalesDialogOpen] = useState(false);
  const [saleToDelete, setSaleToDelete] = useState<Sale | null>(null);
  const [requestSale, setRequestSale] = useState<Sale | null>(null);
  const [requestFormData, setRequestFormData] = useState(initialRequestFormState);
  const [isUpdateConfirmOpen, setIsUpdateConfirmOpen] = useState(false);
  const [isDraft, setIsDraft] = useState(false);
  const [isDraftWarningOpen, setIsDraftWarningOpen] = useState(false);
  const [existingDraft, setExistingDraft] = useState<Sale | null>(null);

  // نوع المرفقات (نفس صفحة التوريد)
  interface AttachmentFile {
    originalName: string;
    fileName: string;
    filePath: string;
    size: number;
    type: string;
    uploadedAt: string;
  }

  const [attachments, setAttachments] = useState<AttachmentFile[]>([]);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [isAttachmentsModalOpen, setIsAttachmentsModalOpen] = useState(false);
  const [exportColumns, setExportColumns] = useState({
    saleNumber: true,
    date: true,
    client: true,
    warehouse: true,
    serialNumber: true,
    model: true,
    condition: true,
    warranty: true,
    barcode: true
  });

  const fileInputRef = useRef<HTMLInputElement>(null);
  const attachmentInputRef = useRef<HTMLInputElement>(null);
  const attachmentsInputRef = useRef<HTMLInputElement>(null);
  
  // تم حذف متغيرات المعاينة القديمة - يتم استخدام AttachmentsViewer الآن

  // متغير لتخزين حالة وجود مسودة محفوظة
  const [hasSavedDraft, setHasSavedDraft] = useState(false);
  // Temporary permissions (remove when auth system is implemented)
  const permissions = { create: true, edit: true, delete: true, view: true, viewAll: true };

  
  // تم حذف دالة previewAttachment - يتم استخدام AttachmentsViewer الآن

  // تحديد المخزن الافتراضي استنادًا إلى صلاحيات المستخدم
  const determineDefaultWarehouse = () => {
    if (!formState.warehouseId && currentUser && warehouses.length > 0) {
      // تحقق من وجود مخازن مرتبطة بالمستخدم
      const userAccessWarehouses = warehouses.filter(w => currentUser.warehouseAccess?.includes(w.id));
      
      if (userAccessWarehouses.length > 0) {
        // إذا وجدت مخازن يملك المستخدم صلاحية الوصول إليها، اختر الأول
        return userAccessWarehouses[0].id.toString();
      } else if (systemSettings?.defaultWarehouseId) {
        // إذا كان هناك مخزن افتراضي في إعدادات النظام
        return systemSettings.defaultWarehouseId.toString();
      } else if (warehouses.length > 0) {
        // إذا لم يوجد، اختر أول مخزن متاح
        return warehouses[0].id.toString();
      }
    }
    return formState.warehouseId;
  };

  useEffect(() => {
    if (!loadedSale) {
      const maxId = sales.reduce((max, s) => (s.id > max ? s.id : max), 0);
      const newId = maxId + 1;
      setSoNumber(`SO-${newId}`);
      
      // تعيين المخزن الافتراضي عند تحميل الصفحة لأول مرة
      const defaultWarehouseId = determineDefaultWarehouse();
      if (defaultWarehouseId && !formState.warehouseId) {
        setFormState(prev => ({ 
          ...prev, 
          warehouseId: defaultWarehouseId 
        }));
      }
    } else {
      setSoNumber(loadedSale.soNumber);
    }
    
    // فحص وجود مسودة عند تحميل الصفحة
    if (typeof window !== 'undefined') {
      const hasDraft = !!localStorage.getItem('saleOrderDraft');
      setHasSavedDraft(hasDraft);
    }
  }, [sales, loadedSale, warehouses, currentUser]);

  const resetPage = (preserveOpNumber = false) => {
    // ✅ الحفاظ على opNumber إذا طُلب ذلك
    const currentOpNumber = preserveOpNumber ? formState.opNumber : '';

    setFormState({
      ...initialFormState,
      opNumber: currentOpNumber, // ✅ الحفاظ على رقم الأمر إذا كان موجوداً
    });
    setInvoiceItems([]);
    setSerialNumber('');
    setLoadedSale(null);
    setAttachments([]); // ← مسح المرفقات
    setIsCreateMode(false); // ← إلغاء وضع الإنشاء
  };

  // دالة فحص المسودات الموجودة في localStorage
  const checkExistingDrafts = () => {
    if (typeof window === 'undefined') return false;

    try {
      const draftData = localStorage.getItem('saleOrderDraft');
      if (draftData) {
        const draft = JSON.parse(draftData);

        // إنشاء كائن مسودة للعرض
        const draftSale: Sale = {
          id: 0, // معرف مؤقت
          soNumber: soNumber || 'جديد', // الاحتفاظ بنفس الرقم
          opNumber: draft.formState?.opNumber || '',
          date: draft.formState?.date || new Date().toISOString().slice(0, 16),
          clientName: draft.formState?.clientId ? clients.find(c => c.id.toString() === draft.formState.clientId)?.name || '' : '',
          warehouseName: draft.formState?.warehouseId ? warehouses.find(w => w.id.toString() === draft.formState.warehouseId)?.name || '' : '',
          items: draft.invoiceItems || [],
          notes: draft.formState?.notes || '',
          warrantyPeriod: draft.formState?.warrantyPeriod || 'none',
          employeeName: currentUser?.name || '',
        };

        setExistingDraft(draftSale);
        setIsDraftWarningOpen(true);
        return true;
      }
    } catch (error) {
      console.error('خطأ في قراءة المسودة:', error);
    }

    return false;
  };

  // دالة استكمال المسودة الموجودة
  const continueDraft = () => {
    try {
      // تحميل المسودة من localStorage
      const draftData = localStorage.getItem('saleOrderDraft');
      if (draftData) {
        const draft = JSON.parse(draftData);

        // تحميل البيانات مع حماية من القيم undefined
        setFormState({
          ...initialFormState,
          ...(draft.formState || {}),
          // التأكد من أن جميع الحقول لها قيم صحيحة
          opNumber: draft.formState?.opNumber || '',
          date: draft.formState?.date || new Date().toISOString().slice(0, 16),
          clientId: draft.formState?.clientId || '',
          warehouseId: draft.formState?.warehouseId || '',
          notes: draft.formState?.notes || '',
          warrantyPeriod: draft.formState?.warrantyPeriod || 'none',
        });
        setInvoiceItems(draft.invoiceItems || []);
        setAttachments(draft.attachments || []);

        setIsDraftWarningOpen(false);
        setIsDraft(true);

        toast({
          title: 'تم تحميل المسودة',
          description: `تم تحميل مسودة فاتورة البيع رقم ${soNumber || 'جديد'}`,
        });
      }
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'خطأ في تحميل المسودة',
        description: 'حدث خطأ أثناء تحميل المسودة',
      });
    }
  };

  // دالة حذف المسودة والمتابعة مع فاتورة جديدة
  const deleteDraftAndProceed = () => {
    try {
      // حذف المسودة من localStorage
      localStorage.removeItem('saleOrderDraft');

      setIsDraftWarningOpen(false);
      setExistingDraft(null);
      setIsDraft(false);
      setHasSavedDraft(false);
      handleCreateNew();

      toast({
        title: 'تم حذف المسودة',
        description: 'تم حذف المسودة السابقة وبدء فاتورة جديدة',
      });
    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'خطأ في حذف المسودة',
        description: 'حدث خطأ أثناء حذف المسودة',
      });
    }
  };

  const handleCreateNew = () => {
    // فحص وجود مسودات قبل البدء
    if (checkExistingDrafts()) {
      return; // إيقاف العملية وعرض التنبيه
    }

    // المتابعة مع إنشاء فاتورة جديدة
    proceedWithNewSale();
  };

  // دالة المتابعة مع إنشاء فاتورة جديدة
  const proceedWithNewSale = () => {
    // ✅ الحفاظ على opNumber عند إنشاء فاتورة جديدة
    const hasOpNumber = formState.opNumber && formState.opNumber.trim() !== '';
    resetPage(hasOpNumber); // ✅ تمرير معامل للحفاظ على opNumber
    setIsCreateMode(true); // ← تفعيل وضع الإنشاء
    toast({ title: 'جاهز للبدء', description: 'تم تجهيز فاتورة جديدة.' });
  };

  const handleCancel = () => {
    resetPage();
    setIsCancelAlertOpen(false);
    toast({ title: 'تم الإلغاء', description: 'تم إلغاء العملية الحالية.' });
  };

  const handleLoadSale = (sale: Sale) => {
    setLoadedSale(sale);
    setIsCreateMode(false); // ← إلغاء وضع الإنشاء عند تحميل فاتورة موجودة
    const client = clients.find((c) => c.name === sale.clientName);
    const warehouse = warehouses.find((w) => w.name === sale.warehouseName);

    setFormState({
      opNumber: sale.opNumber || '',
      date: sale.date || new Date().toISOString().slice(0, 16),
      clientId: client?.id.toString() || '',
      warehouseId: warehouse?.id.toString() || '',
      notes: sale.notes || '',
      warrantyPeriod: sale.warrantyPeriod || 'none',
    });
    setInvoiceItems(sale.items);

    // تحويل المرفقات القديمة إلى تنسيق AttachmentFile الجديد
    if (sale.attachments) {
      let attachmentsArray: any[] = [];

      // التحقق من نوع البيانات وتحويلها إلى مصفوفة
      if (typeof sale.attachments === 'string') {
        try {
          attachmentsArray = JSON.parse(sale.attachments);
        } catch (error) {
          console.warn('فشل في تحليل المرفقات:', error);
          attachmentsArray = [];
        }
      } else if (Array.isArray(sale.attachments)) {
        attachmentsArray = sale.attachments;
      }

      if (attachmentsArray.length > 0 && typeof attachmentsArray[0] === 'string') {
        // تحويل من النوع القديم (string[]) إلى الجديد (AttachmentFile[])
        const convertedAttachments: AttachmentFile[] = attachmentsArray.map(fileName => ({
          originalName: fileName,
          fileName: fileName,
          filePath: `/attachments/sales/${fileName}`,
          size: 0,
          type: 'application/octet-stream',
          uploadedAt: sale.createdAt || new Date().toISOString()
        }));
        setAttachments(convertedAttachments);
      } else if (attachmentsArray.length > 0) {
        // النوع الجديد بالفعل
        setAttachments(attachmentsArray as AttachmentFile[]);
      } else {
        setAttachments([]);
      }
    } else {
      setAttachments([]);
    }

    setIsViewSalesDialogOpen(false);
    toast({
      title: 'تم التحميل',
      description: `تم تحميل الفاتورة ${sale.soNumber} للتعديل`,
    });
  };

  const handleDeleteSale = () => {
    if (saleToDelete) {
      try {
        // فحص العلاقات قبل الحذف
        const relationCheck = checkSaleRelations(saleToDelete.id);
        if (!relationCheck.canDelete) {
          toast({
            variant: 'destructive',
            title: 'لا يمكن حذف الفاتورة',
            description: `${relationCheck.reason}${relationCheck.relatedOperations ? '\nالعمليات المرتبطة: ' + relationCheck.relatedOperations.join(', ') : ''}`,
          });
          setSaleToDelete(null);
          return;
        }

        deleteSale(saleToDelete.id);
        toast({
          variant: 'destructive',
          title: 'تم الحذف',
          description: `تم حذف الفاتورة ${saleToDelete.soNumber}`,
        });

        if (loadedSale && loadedSale.id === saleToDelete.id) {
          resetPage();
        }
        setSaleToDelete(null);
      } catch (error) {
        toast({
          variant: 'destructive',
          title: 'خطأ في الحذف',
          description: error instanceof Error ? error.message : 'حدث خطأ غير متوقع',
        });
        setSaleToDelete(null);
      }
    }
  };

  const handleAddItem = () => {
    if (!serialNumber) return;

    if (!formState.clientId || !formState.warehouseId) {
      toast({
        variant: 'destructive',
        title: 'بيانات غير مكتملة',
        description: 'يرجى اختيار العميل والمخزن أولاً.',
      });
      return;
    }

    // تنظيف الإدخال من الأحرف غير الرقمية إذا كان IMEI
    const cleanedSerialNumber = serialNumber.trim();

    const device = devices.find((d) => d.id === cleanedSerialNumber);

    if (!device) {
      toast({
        title: 'جهاز غير موجود',
        description: 'هذا الرقم التسلسلي غير مسجل في النظام.',
        variant: 'destructive',
      });
      return;
    }
    
    // فحص شامل لحالات الأجهزة المحظورة للبيع
    const deviceStatusCheck = checkDeviceAvailabilityForSale(device);
    if (!deviceStatusCheck.canSell) {
      toast({
        title: 'جهاز غير متاح للبيع',
        description: deviceStatusCheck.reason,
        variant: 'destructive',
      });
      return;
    }


    const selectedWarehouseId = parseInt(formState.warehouseId, 10);
    if (device.warehouseId !== selectedWarehouseId) {
      const deviceWarehouse = warehouses.find(
        (w) => w.id === device.warehouseId
    );
      toast({
        title: 'مخزن غير صحيح',
        description: `هذا الجهاز موجود في '${deviceWarehouse?.name || 'مخزن غير معروف'}', وليس في المخزن المحدد.`,
        variant: 'destructive',
      });
      return;
    }

    if (invoiceItems.some((item) => item.deviceId === cleanedSerialNumber)) {
      toast({
        title: 'الجهاز مضاف بالفعل',
        description: 'هذا الجهاز موجود بالفعل في الفاتورة.',
        variant: 'destructive',
      });
      return;
    }

    const newItem: SaleItem = {
      deviceId: device.id,
      model: device.model,
      price: device.price,
      condition: device.condition,
    };

    setInvoiceItems((prev) => [...prev, newItem]);
    setSerialNumber('');
    toast({
      title: 'تمت الإضافة',
      description: `تمت إضافة جهاز ${device.model} للفاتورة.`,
    });
  };

  const handleFileImport = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!formState.clientId || !formState.warehouseId) {
      toast({
        variant: 'destructive',
        title: 'بيانات غير مكتملة',
        description: 'يرجى اختيار العميل والمخزن أولاً قبل الاستيراد.',
      });
      return;
    }

    const text = await file.text();
    const lines = text
      .split(/\r?\n/)
      .map((line) => line.trim())
      .filter((line) => line.length > 0);

    let importedCount = 0;
    let duplicateCount = 0;
    let invalidCount = 0;
    let unavailableCount = 0; // عداد للأجهزة غير المتاحة للبيع

    const newItems: SaleItem[] = [];
    const existingImeis = new Set(invoiceItems.map((item) => item.deviceId));
    const selectedWarehouseId = parseInt(formState.warehouseId, 10);
    const unavailableDevices: string[] = []; // لتجميع أسماء الأجهزة غير المتاحة

    for (const imei of lines) {
      const device = devices.find((d) => d.id === imei);
      
      // فحص وجود الجهاز
      if (!device) {
        invalidCount++;
        continue;
      }

      // فحص تكرار الجهاز في الفاتورة
      if (existingImeis.has(imei)) {
        duplicateCount++;
        continue;
      }

      // فحص المخزن
      if (device.warehouseId !== selectedWarehouseId) {
        invalidCount++;
        continue;
      }

      // فحص إمكانية البيع باستخدام الدالة الجديدة
      const deviceStatusCheck = checkDeviceAvailabilityForSale(device);
      if (!deviceStatusCheck.canSell) {
        unavailableCount++;
        unavailableDevices.push(`${device.id} (${device.status})`);
        continue;
      }

      const newItem: SaleItem = {
        deviceId: device.id,
        model: device.model,
        price: device.price,
        condition: device.condition,
      };
      newItems.push(newItem);
      existingImeis.add(imei);
      importedCount++;
    }

    if (newItems.length > 0) {
      setInvoiceItems((prev) => [...prev, ...newItems]);
    }

    // إنشاء رسالة مفصلة عن النتائج
    let resultMessage = `تمت إضافة ${importedCount} جهازاً بنجاح.`;
    
    const skippedItems = [];
    if (duplicateCount > 0) skippedItems.push(`${duplicateCount} مكرر`);
    if (invalidCount > 0) skippedItems.push(`${invalidCount} غير صالح`);
    if (unavailableCount > 0) skippedItems.push(`${unavailableCount} غير متاح للبيع`);
    
    if (skippedItems.length > 0) {
      resultMessage += ` تم تخطي ${skippedItems.join(' + ')} جهازاً.`;
    }

    // إضافة تفاصيل الأجهزة غير المتاحة
    if (unavailableDevices.length > 0) {
      resultMessage += `\n\nالأجهزة غير المتاحة: ${unavailableDevices.slice(0, 5).join(', ')}`;
      if (unavailableDevices.length > 5) {
        resultMessage += ` وأجهزة أخرى...`;
      }
    }

    toast({
      title: 'اكتمل الاستيراد',
      description: resultMessage,
      variant: unavailableCount > 0 ? 'default' : 'default',
    });

    if (event.target) event.target.value = '';
  };

  const handleRemoveItem = (deviceId: string) => {
    // العثور على معلومات الجهاز
    const deviceItem = invoiceItems.find(item => item.deviceId === deviceId);
    if (!deviceItem) return;

    // إذا كانت الفاتورة محفوظة مسبقاً، نحتاج لفحص العلاقات
    if (loadedSale) {
      try {
        // فحص العلاقات للجهاز المحدد
        const deviceRelationCheck = checkDeviceRelations(deviceId);
        if (!deviceRelationCheck.canDelete) {
          toast({
            variant: 'destructive',
            title: 'لا يمكن إزالة الجهاز',
            description: `${deviceRelationCheck.reason}${deviceRelationCheck.relatedOperations ? '\nالعمليات المرتبطة: ' + deviceRelationCheck.relatedOperations.join(', ') : ''}`,
          });
          return;
        }
      } catch (error) {
        toast({
          variant: 'destructive',
          title: 'خطأ في فحص العلاقات',
          description: 'حدث خطأ أثناء فحص علاقات الجهاز',
        });
        return;
      }
    }

    // تأكيد الحذف مع عرض معلومات الجهاز
    const confirmMessage = `هل أنت متأكد من إزالة الجهاز؟\n\nالرقم التسلسلي: ${deviceId}\nالموديل: ${deviceItem.model}\nالحالة: ${deviceItem.condition}`;

    if (confirm(confirmMessage)) {
      // إزالة الجهاز من القائمة
      setInvoiceItems((prev) =>
        prev.filter((item) => item.deviceId !== deviceId)
    );

      toast({
        title: 'تم إزالة الجهاز',
        description: `تم إزالة الجهاز ${deviceId} من الفاتورة`,
      });
    }
  };

  const handleSaveClient = () => {
    if (!newClientName.trim()) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'يرجى إدخال اسم العميل.',
      });
      return;
    }
    addContact({ name: newClientName, phone: '', email: '' }, 'client');
    toast({ title: 'تم الحفظ', description: 'تمت إضافة عميل جديد بنجاح.' });
    setIsClientModalOpen(false);
    setNewClientName('');
  };

  // حفظ المسودة الحالية في localStorage فقط (لا نضيف للمتجر)
  const saveDraft = () => {
    // إذا لم تكن هناك أي أجهزة، فلا داعي للحفظ
    if (invoiceItems.length === 0 && !formState.clientId) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'لا توجد بيانات كافية للحفظ كمسودة.',
      });
      return;
    }

    const draftData = {
      formState,
      invoiceItems,
      attachments,
      soNumber,
      opNumber: formState.opNumber,
      timestamp: new Date().toISOString(),
    };

    try {
      // حفظ في localStorage فقط (لا نضيف للمتجر)
      localStorage.setItem('saleOrderDraft', JSON.stringify(draftData));
      setIsDraft(true);
      setHasSavedDraft(true);

      toast({
        title: 'تم حفظ المسودة',
        description: `تم حفظ بيانات فاتورة البيع رقم ${soNumber || 'جديد'} كمسودة بنجاح.`,
      });
    } catch (error) {
      console.error('Error saving draft:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ في الحفظ',
        description: 'حدث خطأ أثناء حفظ المسودة. يرجى المحاولة مرة أخرى.',
      });
    }
  };
  
  // تحميل المسودة المحفوظة من localStorage
  const loadDraft = () => {
    try {
      if (typeof window !== 'undefined') {
        const draftJSON = localStorage.getItem('saleOrderDraft');
        if (!draftJSON) {
          toast({
            variant: 'destructive',
            title: 'لا توجد مسودة',
            description: 'لم يتم العثور على أي مسودة محفوظة.',
          });
          return;
        }
        
        const draftData = JSON.parse(draftJSON);
        
        setFormState({
          ...draftData.formState,
          opNumber: draftData.opNumber || draftData.formState.opNumber || ''
        });
        setInvoiceItems(draftData.invoiceItems);
        setAttachments(draftData.attachments || []);
        setSoNumber(draftData.soNumber || '');
        setLoadedSale(null);
        setSerialNumber('');
        setIsDraft(true);
        
        toast({
          title: 'تم تحميل المسودة',
          description: `تم تحميل المسودة المحفوظة بتاريخ ${new Date(draftData.timestamp).toLocaleString('en-US')}`,
        });
      }
    } catch (error) {
      console.error('Error loading draft:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ في التحميل',
        description: 'حدث خطأ أثناء تحميل المسودة. قد تكون البيانات تالفة.',
      });
    }
  };

  // تأكيد تحديث فاتورة موجودة
  const confirmUpdateSale = () => {
    if (!loadedSale) return;
    
    const client = clients.find(
      (c) => c.id === parseInt(formState.clientId, 10)
    );
    const warehouse = warehouses.find(
      (w) => w.id === parseInt(formState.warehouseId, 10)
    );

    if (!client || !warehouse) {
      toast({
        title: 'خطأ في البيانات',
        description: 'لم يتم العثور على العميل أو المخزن المحدد.',
        variant: 'destructive',
      });
      return;
    }

    const saleData = {
      opNumber: formState.opNumber && formState.opNumber.trim() !== '' ? formState.opNumber.trim() : loadedSale.opNumber, // ✅ استخدام opNumber المدخل أو الاحتفاظ بالقيمة الموجودة
      date: formState.date,
      clientName: client.name,
      warehouseName: warehouse.name,
      items: invoiceItems,
      notes: formState.notes,
      warrantyPeriod: formState.warrantyPeriod,
      attachments: attachments.map(file => file.fileName), // ← تحويل إلى أسماء ملفات للتحديث
    };
    
    updateSale({
      ...saleData,
      id: loadedSale.id,
      soNumber: loadedSale.soNumber,
      employeeName: loadedSale.employeeName || currentUser?.name || 'مدير النظام',
      createdAt: loadedSale.createdAt || new Date().toISOString(),
    });
    
    toast({
      title: 'تم تحديث الفاتورة',
      description: `تم تحديث الفاتورة ${loadedSale.soNumber} بنجاح.`,
    });
    
    setIsUpdateConfirmOpen(false);

    // ✅ الاحتفاظ برقم الأمر إذا كان موجوداً
    const shouldPreserveOpNumber = formState.opNumber && formState.opNumber.trim() !== '';
    resetPage(shouldPreserveOpNumber);
  };

  const handleSaveSale = () => {
    if (
      !formState.clientId ||
      !formState.warehouseId ||
      invoiceItems.length === 0
    ) {
      toast({
        title: 'بيانات غير مكتملة',
        description: 'يرجى اختيار العميل والمخزن وإضافة جهاز واحد على الأقل.',
        variant: 'destructive',
      });
      return;
    }

    const client = clients.find(
      (c) => c.id === parseInt(formState.clientId, 10)
    );
    const warehouse = warehouses.find(
      (w) => w.id === parseInt(formState.warehouseId, 10)
    );

    if (!client || !warehouse) {
      toast({
        title: 'خطأ في البيانات',
        description: 'لم يتم العثور على العميل أو المخزن المحدد.',
        variant: 'destructive',
      });
      return;
    }

    // إذا كان تحديثًا لفاتورة موجودة، نعرض نافذة التأكيد
    if (loadedSale) {
      setIsUpdateConfirmOpen(true);
      return;
    }
    
    // إذا كانت فاتورة جديدة، نستمر بالحفظ مباشرة
    const saleData = {
      opNumber: formState.opNumber && formState.opNumber.trim() !== '' ? formState.opNumber.trim() : undefined, // ✅ إرسال undefined إذا لم يكن هناك opNumber محدد
      date: formState.date,
      clientName: client.name,
      warehouseName: warehouse.name,
      items: invoiceItems,
      notes: formState.notes,
      warrantyPeriod: formState.warrantyPeriod,
      employeeName: currentUser?.name || 'مدير النظام', // ← إضافة اسم الموظف
      attachments: attachments.map(file => file.fileName), // ← تحويل إلى أسماء ملفات فقط
    };

    addSale(saleData);
    toast({
      title: 'تم حفظ أمر البيع',
      description: 'تم إنشاء أمر البيع بنجاح.',
    });

    // مسح المسودة بعد الحفظ الناجح
    localStorage.removeItem('saleOrderDraft');
    setIsDraft(false);
    setHasSavedDraft(false);
    setIsCreateMode(false); // ← إلغاء وضع الإنشاء بعد الحفظ

    // ✅ الاحتفاظ برقم الأمر إذا كان موجوداً
    const shouldPreserveOpNumber = formState.opNumber && formState.opNumber.trim() !== '';
    resetPage(shouldPreserveOpNumber);
  };

  const getPdfHeaderFooter = (doc: jsPDF, settings: SystemSettings) => {
    const addHeader = () => {
      if (settings.logoUrl) {
        try {
          doc.addImage(settings.logoUrl, 'PNG', 15, 10, 20, 20);
        } catch (e) {
          console.error('Error adding logo image to PDF:', e);
        }
      }
      doc
        .setFontSize(16)
        .text(settings.companyName, 190, 15, { align: 'right' });
      doc
        .setFontSize(10)
        .text(settings.companyAddress, 190, 22, { align: 'right' });
      doc.text(settings.contactNumbers, 190, 29, { align: 'right' });
      doc.setLineWidth(0.5).line(15, 35, 195, 35);
    };
    const addFooter = (data: any) => {
      const pageCount = doc.internal.pages.length;
      doc
        .setFontSize(8)
        .text(
          `صفحة ${data.pageNumber} من ${pageCount - 1}`,
          data.settings.margin.left,
          doc.internal.pageSize.height - 10
    );
      if (settings.reportFooter) {
        doc.text(
          settings.reportFooter,
          195,
          doc.internal.pageSize.height - 10,
          { align: 'right' }
        );
      }
    };
    return { addHeader, addFooter };
  };

  const handlePrint = async (saleToPrint: Sale) => {
    if ((Array.isArray(saleToPrint.items) ? saleToPrint.items.length   : 0) === 0) {
      toast({
        variant: 'destructive',
        title: 'لا يمكن الطباعة',
        description: 'الفاتورة فارغة.',
      });
      return;
    }
    const doc = new jsPDF();
    doc.setR2L(true);
    const { addHeader, addFooter } = getPdfHeaderFooter(doc, systemSettings);

    addHeader();

    doc.setFontSize(20);
    doc.text('فاتورة بيع', 190, 45, { align: 'right' });

    doc.setFontSize(12);
    doc.text(`:العميل`, 190, 52, { align: 'right' });
    doc.text(saleToPrint.clientName, 160, 52, { align: 'right' });

    doc.text(`:التاريخ`, 190, 59, { align: 'right' });
    doc.text(new Date(saleToPrint.date).toLocaleDateString('ar-EG'), 160, 59, {
      align: 'right',
    });

    autoTable(doc, {
      startY: 70,
      head: [['الحالة', 'فترة الضمان', 'الموديل', 'الرقم التسلسلي']],
      body: (Array.isArray(saleToPrint.items) ? saleToPrint.items : []).map((item) => [
        item.condition,
        warrantyMap[saleToPrint.warrantyPeriod] || saleToPrint.warrantyPeriod,
        item.model,
        item.deviceId,
      ]),
      styles: { font: 'Helvetica', halign: 'right' },
      headStyles: { halign: 'center', fillColor: [44, 51, 51] },
      didDrawPage: addFooter,
    });

    doc.output('dataurlnewwindow');
  };

  // دالة لتوليد ملف PDF حسب الخيارات المحددة
  const generateCustomPdf = () => {
    // التأكد من وجود بيانات للتصدير
    if (invoiceItems.length === 0) {
      toast({
        variant: 'destructive',
        title: 'لا توجد بيانات',
        description: 'لا توجد أجهزة لتصديرها.',
      });
      return;
    }
    
    const doc = new jsPDF('p', 'mm', 'a4');
    doc.setR2L(true); // تمكين الكتابة من اليمين إلى اليسار
    
    // إضافة العنوان
    doc.setFontSize(18);
    doc.text('بيانات فاتورة بيع', 105, 20, { align: 'center' });
    
    // إضافة تفاصيل الفاتورة
    doc.setFontSize(12);
    let yPos = 35;
    
    if (exportColumns.saleNumber) {
      doc.text(`رقم الفاتورة: ${soNumber || 'جديد'}`, 200, yPos, { align: 'right' });
      yPos += 7;
    }
    if (exportColumns.date) {
      doc.text(`تاريخ البيع: ${formState.date}`, 200, yPos, { align: 'right' });
      yPos += 7;
    }
    if (exportColumns.client) {
      const clientName = clients.find(c => c.id === parseInt(formState.clientId))?.name || '';
      doc.text(`العميل: ${clientName}`, 200, yPos, { align: 'right' });
      yPos += 7;
    }
    if (exportColumns.warehouse) {
      const warehouseName = warehouses.find(w => w.id === parseInt(formState.warehouseId))?.name || '';
      doc.text(`المخزن: ${warehouseName}`, 200, yPos, { align: 'right' });
      yPos += 7;
    }
    
    // إنشاء مصفوفة الأعمدة والبيانات للجدول
    const selectedColumns: string[] = [];
    const headers: string[] = [];
    
    if (exportColumns.serialNumber) {
      selectedColumns.push('serialNumber');
      headers.push('الرقم التسلسلي');
    }
    if (exportColumns.model) {
      selectedColumns.push('model');
      headers.push('الموديل');
    }
    if (exportColumns.condition) {
      selectedColumns.push('condition');
      headers.push('الحالة');
    }
    if (exportColumns.warranty) {
      selectedColumns.push('warranty');
      headers.push('فترة الضمان');
    }
    
    // بناء مصفوفة البيانات للجدول
    const tableData = invoiceItems.map(item => {
      const rowData = selectedColumns.map(col => {
        if (col === 'serialNumber') return item.deviceId;
        if (col === 'model') return item.model;
        if (col === 'condition') return item.condition;
        if (col === 'warranty') return warrantyMap[formState.warrantyPeriod] || formState.warrantyPeriod;
        if (col === 'barcode') return ''; // الباركود سيتم إضافته بشكل خاص
        return '';
      });
      
      return rowData;
    });
    
    // تحديد ما إذا كان هناك حاجة للباركود
    const includeBarcode = exportColumns.barcode && selectedColumns.includes('barcode');
    
    // رسم الجدول
    autoTable(doc, {
      head: [headers],
      body: tableData,
      startY: 65,
      theme: 'grid',
      styles: { 
        font: 'courier',
        fontSize: 10,
        cellPadding: 3,
        halign: 'right'
      },
      headStyles: {
        fillColor: [66, 135, 245],
        textColor: [255, 255, 255],
        fontSize: 10,
        halign: 'center',
        cellPadding: 3,
      },
      didDrawCell: (data) => {
        // إذا كنا نريد إضافة الباركود وهذه الخلية هي الخلية الأخيرة في الصف وليست في الهيدر
        if (includeBarcode && data.row.index >= 0 && data.section === 'body') {
          try {
            // الحصول على الرقم التسلسلي للجهاز في هذا الصف
            const item = invoiceItems[data.row.index];
            if (item) {
              // استخدام مكتبة JsBarcode لإنشاء الباركود
              const canvas = document.createElement('canvas');
              // تحديد موقع إنشاء الباركود
              const barcodeColumnIndex = headers.indexOf('الباركود');
              if (barcodeColumnIndex >= 0 && data.column.index === barcodeColumnIndex) {
                // إنشاء الباركود وإضافته للخلية
                import('jsbarcode').then((JsBarcode) => {
                  JsBarcode.default(canvas, item.deviceId, {
                    format: "CODE128",
                    height: 15,
                    width: 1,
                    displayValue: false
                  });
                  const imgData = canvas.toDataURL('image/png');
                  
                  // إضافة الباركود للخلية
                  const cellWidth = data.cell.width;
                  const cellPadding = 2;
                  
                  doc.addImage(
                    imgData,
                    'PNG',
                    data.cell.x + cellPadding,
                    data.cell.y + cellPadding,
                    cellWidth - (2 * cellPadding),
                    10
                  );
                }).catch(err => console.error('Error loading JsBarcode:', err));
              }
            }
          } catch (err) {
            console.error('Error adding barcode to PDF:', err);
          }
        }
      }
    });
    
    // إضافة معلومات التذييل
    const finalY = (doc as any).lastAutoTable.finalY || 70;
    doc.text(`عدد الأجهزة: ${invoiceItems.length}`, 200, finalY + 10, { align: 'right' });
    doc.text(`تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}`, 200, finalY + 17, { align: 'right' });
    
    // حفظ الملف
    const filename = `فاتورة-بيع-${soNumber || new Date().getTime()}.pdf`;
    doc.save(filename);
    
    toast({
      title: 'تم التصدير',
      description: 'تم تصدير فاتورة البيع إلى ملف PDF بنجاح.',
    });
    
    setIsExportModalOpen(false);
  };

  const handleExport = async (format: 'pdf' | 'excel') => {
    if (invoiceItems.length === 0) {
      toast({
        variant: 'destructive',
        title: 'لا يمكن التصدير',
        description: 'الفاتورة فارغة.',
      });
      return;
    }

    const client = clients.find((c) => c.id.toString() === formState.clientId);

    if (format === 'pdf') {
      if (exportColumns) {
        setIsExportModalOpen(true); // فتح نافذة خيارات التصدير
        return;
      }
      
      const doc = new jsPDF();
      doc.setR2L(true);
      const { addHeader, addFooter } = getPdfHeaderFooter(doc, systemSettings);

      addHeader();

      doc.setFontSize(20);
      doc.text('فاتورة بيع', 190, 45, { align: 'right'  });
      autoTable(doc, {
        startY: 55,
        head: [['الحالة', 'فترة الضمان', 'الموديل', 'الرقم التسلسلي']],
        body: invoiceItems.map((item) => [
          item.condition,
          warrantyMap[formState.warrantyPeriod] || formState.warrantyPeriod,
          item.model,
          item.deviceId,
        ]),
        styles: { font: 'Helvetica', halign: 'right' },
        headStyles: { halign: 'center', fillColor: [44, 51, 51] },
        didDrawPage: addFooter,
      });
      doc.save(`sale-invoice-${soNumber}.pdf`);
    } else {
      const XLSX = await import('xlsx');
      const finalData = invoiceItems.map((item) => ({
        'رقم الفاتورة': soNumber,
        العميل: client?.name,
        التاريخ: formState.date,
        'فترة الضمان':
          warrantyMap[formState.warrantyPeriod] || formState.warrantyPeriod,
        الحالة: item.condition,
        'الرقم التسلسلي': item.deviceId,
        الموديل: item.model,
      }));
      const worksheet = XLSX.utils.json_to_sheet(finalData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'فاتورة البيع');
      worksheet['!cols'] = Array(7).fill({ wch: 20 });
      XLSX.writeFile(workbook, `sale-invoice-${soNumber}.xlsx`);
    }
  };

  const handleSendRequest = () => {
    if (!requestSale) return;

    addEmployeeRequest({
      relatedOrderType: 'sale',
      relatedOrderId: requestSale.id,
      relatedOrderDisplayId: requestSale.soNumber,
      requestType: requestFormData.requestType,
      priority: requestFormData.priority,
      notes: requestFormData.notes,
      attachmentName: requestFormData.attachmentName,
    });

    toast({
      title: 'تم إرسال الطلب',
      description: 'تم إرسال ملاحظتك إلى الإدارة بنجاح.',
    });
    setRequestSale(null);
    setRequestFormData(initialRequestFormState);
  };

  // فحص الصلاحيات - عرض رسالة عدم وجود صلاحيات
  if (!canView) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-red-600">غير مصرح</CardTitle>
            <CardDescription>
              ليس لديك صلاحية للوصول إلى صفحة المبيعات.
              يرجى التواصل مع مدير النظام للحصول على الصلاحيات المطلوبة.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="sales-page">
      {/* رأس الصفحة المحسن */}
      <div className="header-card mb-6">
        <div className="p-6">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center text-white shadow-lg">
                <span className="text-xl font-bold">💰</span>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                  {loadedSale
                    ? `تعديل الفاتورة ${loadedSale.soNumber}`
                    : 'إنشاء فاتورة بيع'}
                </h1>
                <p className="text-gray-600 dark:text-gray-300 text-sm mt-1">
                  نظام إدارة المبيعات المتقدم مع تتبع شامل للفواتير والعملاء
                </p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {/* شارات الحالة */}
              {isCreateMode && (
                <span className="enhanced-badge bg-gradient-to-r from-green-500 to-emerald-600 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center gap-2">
                  <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                  ✅ وضع الإنشاء
                </span>
              )}
              {isDraft && (
                <span className="enhanced-badge bg-gradient-to-r from-amber-500 to-orange-600 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center gap-2">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                  📝 مسودة
                </span>
              )}

              {/* زر الوضع الليلي */}
              <DarkModeToggle
                size="md"
                variant="outline"
                className="enhanced-button"
              />
            </div>
          </div>
        </div>
      </div>

      <Card className="enhanced-sales-card actions-section animate-fade-in-up">
        <CardContent className="p-6">
          <div className="flex flex-wrap items-center justify-center gap-3">
            {/* تحميل فاتورة سابقة */}
            <Button
              variant="outline"
              onClick={() => setIsViewSalesDialogOpen(true)}
              className="enhanced-button bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 px-6 h-12"
            >
              <FolderOpen className="ml-2 h-5 w-5" />
              📂 تحميل فاتورة سابقة
            </Button>

            {/* تحميل مسودة */}
            <Button
              variant="outline"
              onClick={loadDraft}
              disabled={!hasSavedDraft}
              className={`enhanced-button px-6 h-12 ${
                hasSavedDraft
                  ? 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400'
                  : 'bg-gray-100 border-gray-300 text-gray-400'
              }`}
            >
              <FileDown className="ml-2 h-5 w-5" />
              📄 تحميل مسودة
              {hasSavedDraft && (
                <span className="enhanced-badge bg-amber-100 text-amber-800 px-2 py-1 rounded-full text-xs mr-2">
                  ✅ متوفرة
                </span>
              )}
            </Button>

            {/* أزرار الفاتورة المحملة */}
            {loadedSale && (
              <>
                <Button
                  variant="outline"
                  onClick={() => handlePrint(loadedSale)}
                  className="enhanced-button bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 px-6 h-12"
                >
                  <Printer className="ml-2 h-5 w-5" />
                  🖨️ طباعة
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setRequestSale(loadedSale)}
                  className="enhanced-button bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 px-6 h-12"
                >
                  <MessageSquareQuote className="ml-2 h-5 w-5" />
                  💬 إرسال ملاحظة
                </Button>
                {canDelete && (
                  <Button
                    variant="outline"
                    onClick={() => setSaleToDelete(loadedSale)}
                    className="enhanced-button bg-white border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 px-6 h-12"
                  >
                    <Trash className="ml-2 h-5 w-5" />
                    🗑️ حذف الفاتورة
                  </Button>
                )}
              </>
            )}

            {/* زر إنشاء فاتورة جديدة */}
            {canCreate && (
              <Button
                onClick={handleCreateNew}
                className="enhanced-button bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-8 h-12 font-bold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              >
                <PlusCircle className="ml-2 h-5 w-5" />
                ➕ فاتورة بيع جديدة
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      <Card className="enhanced-sales-card info-section animate-fade-in-up">
        <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 py-4">
          <CardTitle className="text-lg text-green-800 dark:text-green-200 flex items-center">
            <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">1</div>
            <div>
              <div className="font-bold">بيانات الفاتورة الأساسية</div>
              <div className="text-xs text-green-600 dark:text-green-300 font-normal mt-1">المعلومات الأساسية للفاتورة والعميل</div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                📋 رقم أمر البيع (SO)
              </Label>
              <Input value={soNumber} disabled className="enhanced-input h-10 text-sm font-mono" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="orderId" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                🧾 رقم فاتورة البيع الرسمية
              </Label>
              <div className="space-y-2">
                <div className="flex gap-2">
                  <Input
                    id="orderId"
                    className="enhanced-input h-10 text-sm flex-1"
                    placeholder={isCreateMode || loadedSale ? "أدخل رقم الفاتورة الرسمية" : "اضغط 'فاتورة بيع جديدة' للبدء"}
                    value={formState.opNumber || ''}
                    onChange={(e) =>
                      setFormState({ ...formState, opNumber: e.target.value || '' })
                    }
                    disabled={(!isCreateMode && !loadedSale) || (loadedSale && !canEdit)}
                    className="h-8 text-xs disabled:opacity-60 disabled:cursor-not-allowed"
                  />
                  {formState.opNumber && (isCreateMode || loadedSale) && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setFormState({ ...formState, opNumber: '' })}
                      className="h-10 px-3 text-xs"
                      title="مسح رقم الأمر"
                    >
                      ✕
                    </Button>
                  )}
                </div>
                {formState.opNumber && (
                  <div className="flex items-center space-x-2 text-xs text-green-600 dark:text-green-400">
                    <span>✅ سيتم الاحتفاظ برقم الأمر عند إنشاء فاتورة جديدة</span>
                  </div>
                )}
              </div>
            </div>
            <div className="space-y-1">
              <Label htmlFor="dateTime" className="text-xs">التاريخ والوقت</Label>
              <Input
                id="dateTime"
                type="datetime-local"
                value={formState.date || ''}
                onChange={(e) =>
                  setFormState({ ...formState, date: e.target.value || '' })
                }
                disabled={(!isCreateMode && !loadedSale) || (loadedSale && !canEdit) || false}
                className="h-8 text-xs disabled:opacity-60 disabled:cursor-not-allowed"
              />
            </div>
            <div className="space-y-1">
              <Label className="text-xs">الموظف المسؤول</Label>
              <Input value={currentUser?.name || "مدير النظام"} disabled className="h-8 text-xs" />
            </div>
            <div className="space-y-1">
              <Label className="text-xs">العميل</Label>
              <div className="flex gap-1">
                <Popover
                  open={isClientSearchOpen}
                  onOpenChange={setIsClientSearchOpen}
                >
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={isClientSearchOpen}
                      disabled={(!isCreateMode && !loadedSale) || (loadedSale && !canEdit) || false}
                      className="w-full justify-between h-8 text-xs disabled:opacity-60 disabled:cursor-not-allowed"
                    >
                      {formState.clientId
                        ? clients.find(
                            (c) => c.id.toString() === formState.clientId,
                          )?.name
                        : isCreateMode || loadedSale ? 'اختر أو ابحث عن عميل...' : "اضغط 'فاتورة بيع جديدة' للبدء"}
                      <ChevronsUpDown className="mr-1 h-3 w-3 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
                    <Command>
                      <CommandInput placeholder="ابحث عن عميل..." />
                      <CommandList>
                        <CommandEmpty>لم يتم العثور على عميل.</CommandEmpty>
                        <CommandGroup>
                          {clients.map((client) => (
                            <CommandItem
                              key={client.id}
                              value={client.name}
                              onSelect={() => {
                                setFormState((s) => ({
                                  ...s,
                                  clientId: client.id.toString(),
                                }));
                                setIsClientSearchOpen(false);
                              }}
                            >
                              <Check
                                className={cn(
                                  'ml-2 h-4 w-4',
                                  formState.clientId === client.id.toString()
                                    ? 'opacity-100'
                                    : 'opacity-0',
                                )}
                              />
                              {client.name}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
                <Button
                  size="icon"
                  variant="outline"
                  onClick={() => setIsClientModalOpen(true)}
                  className="h-8 w-8"
                >
                  <PlusCircle className="h-3 w-3" />
                </Button>
              </div>
            </div>
            <div className="space-y-1">
              <Label className="text-xs">المخزن</Label>
              <div className="flex gap-1">
                <Select
                  dir="rtl"
                  value={formState.warehouseId}
                  onValueChange={(value) =>
                    setFormState({ ...formState, warehouseId: value })
                  }
                  disabled={(!isCreateMode && !loadedSale) || (loadedSale && !canEdit)}
                >
                  <SelectTrigger className="h-8 text-xs disabled:opacity-60 disabled:cursor-not-allowed">
                    <SelectValue placeholder={isCreateMode || loadedSale ? "اختر المخزن" : "اضغط 'فاتورة بيع جديدة' للبدء"} />
                  </SelectTrigger>
                  <SelectContent>
                    {/* عرض المخازن التي لدى المستخدم صلاحية الوصول إليها أولاً */}
                    {currentUser?.warehouseAccess && currentUser.warehouseAccess.length > 0 ? (
                      <>
                        <SelectGroup>
                          <SelectLabel>المخازن المتاحة لك</SelectLabel>
                          {warehouses
                            .filter(w => currentUser.warehouseAccess?.includes(w.id))
                            .map((w) => (
                              <SelectItem key={w.id} value={w.id.toString()}>
                                {w.name}
                              </SelectItem>
                            ))}
                        </SelectGroup>
                        
                        {/* عرض باقي المخازن إذا كان هناك مخازن أخرى */}
                        {warehouses.filter(w => !currentUser.warehouseAccess?.includes(w.id)).length > 0 && (
                          <SelectGroup>
                            <SelectLabel>كل المخازن</SelectLabel>
                            {warehouses
                              .filter(w => !currentUser.warehouseAccess?.includes(w.id))
                              .map((w) => (
                                <SelectItem key={w.id} value={w.id.toString()}>
                                  {w.name}
                                </SelectItem>
                              ))}
                          </SelectGroup>
                        )}
                      </>
                    ) : (
                      /* إذا لم تكن هناك صلاحيات محددة، اعرض جميع المخازن */
                      warehouses.map((w) => (
                        <SelectItem key={w.id} value={w.id.toString()}>
                          {w.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                {(systemSettings?.defaultWarehouseId || warehouses.length > 0) && !formState.warehouseId && (
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => {
                      const defaultWarehouseId = determineDefaultWarehouse();
                      if (defaultWarehouseId) {
                        setFormState(prev => ({
                          ...prev,
                          warehouseId: defaultWarehouseId
                        }));
                        
                        const selectedWarehouse = warehouses.find(w => w.id.toString() === defaultWarehouseId);
                        toast({
                          title: 'تم اختيار المخزن الافتراضي',
                          description: `تم اختيار ${selectedWarehouse?.name || 'المخزن الافتراضي'}`
                        });
                      }
                    }}
                    title="اختيار المخزن الافتراضي"
                    className="h-8 w-8"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"/><path d="m8 12 3 3 5-5"/></svg>
                  </Button>
                )}
              </div>
            </div>
            <div className="space-y-1">
              <Label className="text-xs">فترة الضمان</Label>
              <Select
                dir="rtl"
                value={formState.warrantyPeriod}
                onValueChange={(value) =>
                  setFormState({ ...formState, warrantyPeriod: value })
                }
              >
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="اختر الفترة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">بدون ضمان</SelectItem>
                  <SelectItem value="3d">3 أيام</SelectItem>
                  <SelectItem value="1w">أسبوع</SelectItem>
                  <SelectItem value="1m">شهر واحد</SelectItem>
                  <SelectItem value="3m">3 أشهر</SelectItem>
                  <SelectItem value="6m">6 أشهر</SelectItem>
                  <SelectItem value="1y">سنة واحدة</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* المرفقات - نفس تصميم صفحة التوريد */}
            <div className="w-32 space-y-1">
              <Label className="text-xs">المرفقات</Label>
              <div className="flex gap-1">
                <input
                  type="file"
                  ref={attachmentsInputRef}
                  className="hidden"
                  multiple
                  onChange={async (e) => {
                    const files = Array.from(e.target.files || []);
                    if (files.length === 0) return;

                    // التحقق من وضع الإنشاء
                    if (!isCreateMode && !loadedSale) {
                      toast({
                        variant: 'destructive',
                        title: 'غير متاح',
                        description: "اضغط 'فاتورة بيع جديدة' للبدء.",
                      });
                      return;
                    }

                    try {
                      const formData = new FormData();
                      files.forEach((file) => {
                        formData.append('files', file);
                      });
                      formData.append('section', 'sales');

                      const response = await fetch('/api/upload', {
                        method: 'POST',
                        headers: getAuthHeader(), // ✅ إضافة header التفويض
                        body: formData,
                      });

                      if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                      }

                      const result = await response.json();
                      console.log('Upload response:', result);

                      if (result.success) {
                        setAttachments((prev) => [...prev, ...result.files]);
                        toast({
                          title: 'تم إرفاق الملفات',
                          description: result.message,
                          className: "bg-green-50 border-green-200 text-green-800",
                        });
                      } else {
                        console.error('Upload error:', result);
                        toast({
                          variant: 'destructive',
                          title: 'خطأ في الرفع',
                          description: result.error || result.message || 'فشل في رفع الملفات',
                        });
                      }
                    } catch (error) {
                      console.error('Upload exception:', error);
                      toast({
                        variant: 'destructive',
                        title: 'خطأ في الرفع',
                        description: `حدث خطأ أثناء رفع الملفات: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`,
                      });
                    }

                    if (e.target) e.target.value = '';
                  }}
                />
                <Button
                  variant="outline"
                  onClick={() => attachmentsInputRef.current?.click()}
                  className="h-8 flex-1 text-xs border-indigo-300 text-indigo-600 hover:bg-indigo-50 hover:border-indigo-400 disabled:border-gray-200 disabled:text-gray-400 transition-all duration-200"
                  disabled={(!isCreateMode && !loadedSale) || (loadedSale && !canEdit)}
                >
                  <Upload className="ml-1 h-2 w-2" />
                  {attachments.length > 0 ? `${attachments.length}` : '0'}
                </Button>
                {attachments.length > 0 && (
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setIsAttachmentsModalOpen(true)}
                    className="h-8 w-8 border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400 transition-all duration-200"
                  >
                    <Eye className="h-2 w-2" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="enhanced-stocktake-card supplier-section animate-fade-in-up">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-sky-50 dark:from-blue-950/20 dark:to-sky-950/20 py-4">
          <CardTitle className="text-lg text-blue-800 dark:text-blue-200 flex items-center">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-sky-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">2</div>
            <div>
              <div className="font-bold">إضافة أجهزة للفاتورة</div>
              <div className="text-xs text-blue-600 dark:text-blue-300 font-normal mt-1">إدخال بيانات الأجهزة المباعة</div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          {/* Single line device input */}
          <div className="flex gap-2 items-end">
            {/* IMEI Input - Smaller */}
            <div className="flex-1 min-w-[200px] max-w-[300px]">
              <Label htmlFor="imei-input" className="text-xs">إدخال IMEI</Label>
              <Input
                id="imei-input"
                placeholder={isCreateMode || loadedSale ? "أدخل الرقم التسلسلي..." : "اضغط 'فاتورة بيع جديدة' للبدء"}
                value={serialNumber}
                onChange={(e) => setSerialNumber(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleAddItem()}
                disabled={(!isCreateMode && !loadedSale) || (loadedSale && !canEdit) || false}
                className={`font-mono text-sm disabled:opacity-60 disabled:cursor-not-allowed ${
                  !isCreateMode && !loadedSale
                    ? 'bg-gray-100'
                    : serialNumber.length === 15
                    ? 'border-green-500 bg-green-50'
                    : serialNumber.length > 0
                    ? 'border-yellow-500 bg-yellow-50'
                    : 'hover:border-blue-300'
                }`}
              />
            </div>

            {/* Add Button */}
            <Button
              onClick={handleAddItem}
              disabled={!serialNumber.trim() || (!canCreate && !canEdit) || (!isCreateMode && !loadedSale)}
              size="sm"
              className="bg-blue-500 hover:bg-blue-600 text-white px-3 disabled:opacity-50 disabled:cursor-not-allowed"
              title={
                !isCreateMode && !loadedSale
                  ? "اضغط 'فاتورة بيع جديدة' للبدء"
                  : !canCreate && !canEdit
                  ? "ليس لديك صلاحية لإضافة الأجهزة"
                  : "إضافة الجهاز للفاتورة"
              }
            >
              <PlusCircle className="h-4 w-4" />
            </Button>

            {/* File Upload Button - Small */}
            <div>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileImport}
                accept=".txt"
                className="hidden"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  if (!isCreateMode && !loadedSale) {
                    toast({
                      variant: 'destructive',
                      title: 'غير متاح',
                      description: "اضغط 'فاتورة بيع جديدة' للبدء.",
                    });
                    return;
                  }
                  if (!canCreate && !canEdit) {
                    toast({
                      variant: 'destructive',
                      title: 'غير مصرح',
                      description: 'ليس لديك صلاحية لرفع الملفات.',
                    });
                    return;
                  }
                  if (!formState.clientId || !formState.warehouseId) {
                    toast({
                      variant: 'destructive',
                      title: 'بيانات غير مكتملة',
                      description: 'يرجى اختيار العميل والمخزن أولاً قبل الاستيراد.',
                    });
                    return;
                  }
                  fileInputRef.current?.click()
                }}
                disabled={(!canCreate && !canEdit) || (!isCreateMode && !loadedSale)}
                className="border-green-300 text-green-600 hover:bg-green-50 px-2 disabled:opacity-50 disabled:cursor-not-allowed"
                title={
                  !isCreateMode && !loadedSale
                    ? "اضغط 'فاتورة بيع جديدة' للبدء"
                    : !canCreate && !canEdit
                    ? "ليس لديك صلاحية لرفع الملفات"
                    : "رفع من ملف"
                }
              >
                <Upload className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="enhanced-stocktake-card items-section animate-fade-in-up">
        <CardHeader className="bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-950/20 dark:to-amber-950/20 py-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg text-orange-800 dark:text-orange-200 flex items-center">
              <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-amber-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">3</div>
              <div>
                <div className="font-bold">الأجهزة في الفاتورة</div>
                <div className="text-xs text-orange-600 dark:text-orange-300 font-normal mt-1">قائمة الأجهزة المضافة للفاتورة</div>
              </div>
            </CardTitle>
            <div className="flex items-center space-x-2 space-x-reverse">
              <span className="enhanced-badge bg-gradient-to-r from-orange-500 to-amber-600 text-white px-3 py-1 rounded-full text-sm font-bold">
                📱 {invoiceItems.length} جهاز
              </span>
            </div>
            {invoiceItems.length > 0 && canCreate && (
              <Button
                variant="destructive"
                size="sm"
                onClick={() => {
                  if (confirm('هل أنت متأكد من مسح جميع الأجهزة المضافة؟')) {
                    setInvoiceItems([]);
                    toast({
                      title: "تم المسح",
                      description: "تم مسح جميع الأجهزة المضافة"
                    });
                  }
                }}
                className="bg-red-500 hover:bg-red-600 text-white px-3"
              >
                <Trash2 className="ml-1 h-3 w-3" /> مسح الكل
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {/* Container with fixed height and scroll */}
          <div className="h-[300px] overflow-y-auto border rounded-lg">
            <Table className="border-collapse">
              <TableHeader className="sticky top-0 z-10">
                <TableRow className="bg-gradient-to-r from-orange-50 to-amber-50 border-b-2 border-orange-200">
                  <TableHead className="border-b border-gray-300 text-right font-semibold text-gray-700 bg-orange-100/90 py-2 text-sm">#</TableHead>
                  <TableHead className="border-b border-gray-300 text-right font-semibold text-gray-700 bg-orange-100/90 py-2 text-sm">الرقم التسلسلي</TableHead>
                  <TableHead className="border-b border-gray-300 text-right font-semibold text-gray-700 bg-orange-100/90 py-2 text-sm">الموديل</TableHead>
                  <TableHead className="border-b border-gray-300 text-right font-semibold text-gray-700 bg-orange-100/90 py-2 text-sm">الحالة</TableHead>
                  <TableHead className="border-b border-gray-300 text-right font-semibold text-gray-700 bg-orange-100/90 py-2 text-sm">تاريخ البيع</TableHead>
                  <TableHead className="border-b border-gray-300 text-right font-semibold text-gray-700 bg-orange-100/90 py-2 text-sm">فترة الضمان</TableHead>
                  <TableHead className="border-b border-gray-300 text-center font-semibold text-gray-700 bg-orange-100/90 py-2 text-sm">باركود</TableHead>
                  <TableHead className="border-b border-gray-300 text-center font-semibold text-gray-700 bg-orange-100/90 py-2 text-sm">إجراء</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invoiceItems.length === 0 ? (
                  <TableRow className="hover:bg-gray-50">
                    <TableCell
                      colSpan={8}
                      className="h-32 text-center border-b border-gray-200 text-gray-500 italic"
                    >
                      لم يتم إضافة أجهزة بعد.
                    </TableCell>
                  </TableRow>
                ) : (
                  invoiceItems.map((device, index) => (
                    <TableRow
                      key={device.deviceId}
                      className="hover:bg-orange-50 transition-colors duration-200 cursor-pointer h-12"
                    >
                      <TableCell className="border-b border-gray-200 text-right font-medium py-2 text-sm">{index + 1}</TableCell>
                      <TableCell className="border-b border-gray-200 text-right font-mono text-blue-600 font-medium py-2 text-sm" dir="ltr">{device.deviceId}</TableCell>
                      <TableCell className="border-b border-gray-200 text-right py-2 text-sm">{device.model}</TableCell>
                      <TableCell className="border-b border-gray-200 text-right py-2 text-sm">{device.condition}</TableCell>
                      <TableCell className="border-b border-gray-200 text-right py-2 text-sm">
                        {new Date(formState.date).toLocaleDateString('ar-EG')}
                      </TableCell>
                      <TableCell className="border-b border-gray-200 text-right py-2 text-sm">
                        {warrantyMap[formState.warrantyPeriod] || formState.warrantyPeriod}
                      </TableCell>
                      <TableCell className="border-b border-gray-200 text-center py-2">
                        <Barcode
                          value={device.deviceId}
                          height={25}
                          displayValue={false}
                          margin={0}
                        />
                      </TableCell>
                      <TableCell className="border-b border-gray-200 text-center py-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="text-red-500 hover:text-red-600 hover:bg-red-50 h-8 w-8 p-0 transition-all duration-200"
                          onClick={() => handleRemoveItem(device.deviceId)}
                          title={`إزالة الجهاز ${device.deviceId} من الفاتورة`}
                          disabled={!canCreate && !canEdit}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <Card className="enhanced-stocktake-card summary-section animate-fade-in-up">
        <CardHeader className="bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-950/20 dark:to-violet-950/20 py-4">
          <CardTitle className="text-lg text-purple-800 dark:text-purple-200 flex items-center">
            <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-violet-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">4</div>
            <div>
              <div className="font-bold">ملاحظات الفاتورة</div>
              <div className="text-xs text-purple-600 dark:text-purple-300 font-normal mt-1">إضافة ملاحظات وتعليقات حول الفاتورة</div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder={isCreateMode || loadedSale ? "ملاحظات إضافية على الفاتورة..." : "اضغط 'فاتورة بيع جديدة' للبدء"}
            value={formState.notes || ''}
            onChange={(e) =>
              setFormState({ ...formState, notes: e.target.value || '' })
            }
            disabled={(!isCreateMode && !loadedSale) || (loadedSale && !canEdit)}
            className="min-h-[80px] resize-none disabled:opacity-60 disabled:cursor-not-allowed disabled:bg-gray-100"
          />
        </CardContent>
      </Card>

      <Card className="enhanced-stocktake-card actions-section animate-fade-in-up">
        <CardHeader className="bg-gradient-to-r from-teal-50 to-cyan-50 dark:from-teal-950/20 dark:to-cyan-950/20 py-4">
          <CardTitle className="text-lg text-teal-800 dark:text-teal-200 flex items-center">
            <div className="w-8 h-8 bg-gradient-to-br from-teal-500 to-cyan-600 text-white rounded-xl flex items-center justify-center text-sm font-bold mr-3 enhanced-icon">5</div>
            <div>
              <div className="font-bold">خيارات التصدير والطباعة</div>
              <div className="text-xs text-teal-600 dark:text-teal-300 font-normal mt-1">تصدير وطباعة الفاتورة بصيغ مختلفة</div>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-5 gap-2 pt-2">
          <Button
            variant={invoiceItems.length === 0 ? "outline" : "default"}
            onClick={() => setIsExportModalOpen(true)}
            disabled={invoiceItems.length === 0}
            className="h-20 flex flex-col gap-1 justify-center"
            title="تصدير مخصص كملف PDF"
          >
            <FileDown className="h-6 w-6" />
            <span className="text-xs">تصدير PDF مخصص</span>
          </Button>
          <Button
            variant={invoiceItems.length === 0 ? "outline" : "default"}
            onClick={() => {
              const doc = new jsPDF();
              doc.setR2L(true);
              const { addHeader, addFooter } = getPdfHeaderFooter(doc, systemSettings);

              addHeader();

              doc.setFontSize(20);
              doc.text('فاتورة بيع', 190, 45, { align: 'right'  });
              autoTable(doc, {
                startY: 55,
                head: [['الحالة', 'فترة الضمان', 'الموديل', 'الرقم التسلسلي']],
                body: invoiceItems.map((item) => [
                  item.condition,
                  warrantyMap[formState.warrantyPeriod] || formState.warrantyPeriod,
                  item.model,
                  item.deviceId,
                ]),
                styles: { font: 'Helvetica', halign: 'right' },
                headStyles: { halign: 'center', fillColor: [44, 51, 51] },
                didDrawPage: addFooter,
              });
              doc.save(`فاتورة-بيع-${soNumber || new Date().getTime()}.pdf`);
              
              toast({
                title: 'تم التصدير',
                description: 'تم تصدير الفاتورة إلى PDF بنجاح',
              });
            }}
            disabled={invoiceItems.length === 0}
            className="h-20 flex flex-col gap-1 justify-center"
            title="تصدير سريع كملف PDF"
          >
            <FileDown className="h-6 w-6" />
            <span className="text-xs">PDF سريع</span>
          </Button>
          <Button
            variant={invoiceItems.length === 0 ? "outline" : "default"}
            onClick={() => handleExport('excel')}
            disabled={invoiceItems.length === 0}
            className="h-20 flex flex-col gap-1 justify-center"
            title="تصدير كملف Excel"
          >
            <FileSpreadsheet className="h-6 w-6" />
            <span className="text-xs">تصدير Excel</span>
          </Button>
          <Button
            variant={invoiceItems.length === 0 ? "outline" : "secondary"}
            onClick={() =>
              loadedSale
                ? handlePrint(loadedSale)
                : toast({
                    variant: 'destructive',
                    title: 'يرجى حفظ الفاتورة أولاً',
                  })
            }
            disabled={invoiceItems.length === 0}
            className="h-20 flex flex-col gap-1 justify-center"
            title="طباعة الفاتورة"
          >
            <Printer className="h-6 w-6" />
            <span className="text-xs">طباعة الفاتورة</span>
          </Button>
          {(canCreate || (loadedSale && canEdit)) && !loadedSale && (
            <Button
              variant="outline"
              onClick={() => {
                saveDraft();
              }}
              disabled={(invoiceItems.length === 0 && !formState.clientId) || (!isCreateMode && !loadedSale) || (!!loadedSale && !canEdit && !canCreate)}
              className="h-20 flex flex-col gap-1 justify-center disabled:opacity-50 disabled:cursor-not-allowed"
              title={
                !isCreateMode && !loadedSale
                  ? "اضغط 'فاتورة بيع جديدة' للبدء"
                  : loadedSale && !canEdit && !canCreate
                  ? "ليس لديك صلاحية لحفظ المسودة"
                  : "حفظ مسودة للمتابعة لاحقاً"
              }
            >
              <Save className="h-6 w-6" />
              <span className="text-xs">حفظ مسودة</span>
            </Button>
          )}
        </CardContent>
      </Card>

      <div className="flex flex-wrap justify-between gap-2">
        <Button
          variant="destructive"
          onClick={() => setIsCancelAlertOpen(true)}
        >
          <X className="ml-2 h-4 w-4" /> إلغاء
        </Button>

        {(canCreate || (loadedSale && canEdit)) && (
          <Button
            onClick={handleSaveSale}
            disabled={(!isCreateMode && !loadedSale) || (loadedSale && !canEdit && !canCreate)}
            className="px-8 disabled:opacity-50 disabled:cursor-not-allowed"
            size="lg"
            title={
              !isCreateMode && !loadedSale
                ? "اضغط 'فاتورة بيع جديدة' للبدء"
                : loadedSale && !canEdit && !canCreate
                ? "ليس لديك صلاحية لتحديث الفاتورة"
                : loadedSale ? 'تحديث الفاتورة' : 'قبول وحفظ'
            }
          >
            <Save className="ml-2 h-5 w-5" />{' '}
            {loadedSale ? 'تحديث الفاتورة' : 'قبول وحفظ'}
          </Button>
        )}
      </div>

      {/* View Previous Sales Dialog */}
      <Dialog
        open={isViewSalesDialogOpen}
        onOpenChange={setIsViewSalesDialogOpen}
      >
        <DialogContent className="enhanced-dialog sm:max-w-4xl max-h-[80vh]">
          <DialogHeader className="enhanced-dialog-header">
            <DialogTitle className="enhanced-dialog-title flex items-center space-x-2 space-x-reverse">
              <FolderOpen className="h-5 w-5 text-primary" />
              <span>تحميل فاتورة بيع سابقة</span>
            </DialogTitle>
            <DialogDescription className="enhanced-dialog-description">
              اختر فاتورة بيع لتحميل بياناتها أو إرسال ملاحظة حولها من القائمة أدناه
            </DialogDescription>
          </DialogHeader>

          <div className="enhanced-scroll-area p-4">
            <Table className="enhanced-modal-table">
              <TableHeader className="sticky top-0 z-10">
                <TableRow className="bg-gradient-to-r from-green-50 to-emerald-50 border-b-2 border-green-200">
                  <TableHead className="border-b border-gray-300 text-right font-semibold text-gray-700 bg-green-100/90 py-3 text-sm">
                    📋 رقم الفاتورة
                  </TableHead>
                  <TableHead className="border-b border-gray-300 text-right font-semibold text-gray-700 bg-green-100/90 py-3 text-sm">
                    👤 العميل
                  </TableHead>
                  <TableHead className="border-b border-gray-300 text-right font-semibold text-gray-700 bg-green-100/90 py-3 text-sm">
                    📅 التاريخ
                  </TableHead>
                  <TableHead className="border-b border-gray-300 text-center font-semibold text-gray-700 bg-green-100/90 py-3 text-sm">
                    📱 عدد الأجهزة
                  </TableHead>
                  <TableHead className="border-b border-gray-300 text-center font-semibold text-gray-700 bg-green-100/90 py-3 text-sm">
                    💰 الإجمالي
                  </TableHead>
                  <TableHead className="border-b border-gray-300 text-center font-semibold text-gray-700 bg-green-100/90 py-3 text-sm">
                    ⚙️ إجراءات
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sales.length === 0 ? (
                  <TableRow className="hover:bg-gray-50">
                    <TableCell
                      colSpan={6}
                      className="h-32 text-center border-b border-gray-200 text-gray-500 italic"
                    >
                      لا توجد فواتير سابقة.
                    </TableCell>
                  </TableRow>
                ) : (
                  sales.map((sale, index) => {
                    const total = sale.items.reduce(
                      (sum, item) => sum + item.price,
                      0
    );
                    return (
                      <TableRow
                        key={sale.id}
                        className={`
                          hover:bg-green-50 transition-colors duration-200 cursor-pointer h-12
                          ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}
                        `}
                      >
                        <TableCell className="border-b border-gray-200 text-right font-mono text-blue-600 font-medium py-2 text-sm">
                          {sale.soNumber}
                        </TableCell>
                        <TableCell className="border-b border-gray-200 text-right font-medium text-gray-700 py-2 text-sm">
                          {sale.clientName}
                        </TableCell>
                        <TableCell className="border-b border-gray-200 font-mono text-left text-gray-600 py-2 text-sm">
                          {new Date(sale.date).toLocaleDateString('en-US')}
                        </TableCell>
                        <TableCell className="border-b border-gray-200 text-center py-2">
                          <span className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full text-xs font-medium">
                            {(Array.isArray(sale.items) ? sale.items.length : 0)}
                          </span>
                        </TableCell>
                        <TableCell className="border-b border-gray-200 text-center py-2" dir="ltr">
                          <span className="bg-green-100 text-green-800 px-2 py-0.5 rounded-full text-xs font-medium">
                            ${total.toFixed(2)}
                          </span>
                        </TableCell>
                        <TableCell className="border-b border-gray-200 text-center py-2">
                          <div className="flex gap-1 justify-center">
                            <Button
                              size="sm"
                              onClick={() => handleLoadSale(sale)}
                              className="bg-green-500 hover:bg-green-600 text-white transition-all duration-200 h-8 px-3 text-xs"
                            >
                              <FileDown className="ml-1 h-3 w-3" />
                              تحميل
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handlePrint(sale)}
                              className="h-8 px-3 text-xs"
                            >
                              <Printer className="ml-1 h-3 w-3" />
                              طباعة
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">إغلاق</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Client Dialog */}
      <Dialog open={isClientModalOpen} onOpenChange={setIsClientModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>إضافة عميل جديد</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <Label htmlFor="client-name">اسم العميل</Label>
            <Input
              id="client-name"
              value={newClientName}
              onChange={(e) => setNewClientName(e.target.value)}
            />
          </div>
          <DialogFooter>
            <Button onClick={handleSaveClient}>حفظ</Button>
            <DialogClose asChild>
              <Button variant="outline">إلغاء</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Cancel Confirmation Dialog */}
      <AlertDialog open={isCancelAlertOpen} onOpenChange={setIsCancelAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد؟</AlertDialogTitle>
          </AlertDialogHeader>
          <AlertDialogDescription>
            سيتم فقدان جميع البيانات غير المحفوظة في الفاتورة الحالية.
          </AlertDialogDescription>
          <AlertDialogFooter>
            <AlertDialogCancel>تراجع</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleCancel}
              className="bg-destructive hover:bg-destructive/90"
            >
              متابعة الإلغاء
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete Sale Confirmation Dialog */}
      <AlertDialog
        open={!!saleToDelete}
        onOpenChange={() => setSaleToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد؟</AlertDialogTitle>
          </AlertDialogHeader>
          <AlertDialogDescription>
            سيؤدي هذا إلى حذف الفاتورة بشكل دائم وإعادة الأجهزة المرتبطة بها إلى
            المخزون كـ "متاح للبيع".
          </AlertDialogDescription>
          <AlertDialogFooter>
            <AlertDialogCancel>تراجع</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteSale}
              className="bg-destructive hover:bg-destructive/90"
            >
              متابعة الحذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Employee Request Dialog */}
      <Dialog open={!!requestSale} onOpenChange={() => setRequestSale(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>إرسال ملاحظة للإدارة</DialogTitle>
            <DialogDescription>
              بخصوص الفاتورة رقم: {requestSale?.soNumber}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>نوع الطلب</Label>
                <Select
                  dir="rtl"
                  value={requestFormData.requestType}
                  onValueChange={(v: EmployeeRequestType) =>
                    setRequestFormData((s) => ({ ...s, requestType: v }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="تعديل">تعديل</SelectItem>
                    <SelectItem value="إعادة نظر">إعادة نظر</SelectItem>
                    <SelectItem value="حذف">حذف</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>الأولوية</Label>
                <Select
                  dir="rtl"
                  value={requestFormData.priority}
                  onValueChange={(v: EmployeeRequestPriority) =>
                    setRequestFormData((s) => ({ ...s, priority: v }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="عادي">عادي</SelectItem>
                    <SelectItem value="طاريء">طاريء</SelectItem>
                    <SelectItem value="طاريء جدا">طاريء جداً</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label>تفاصيل المشكلة / الملاحظة</Label>
              <Textarea
                placeholder="اشرح المشكلة أو الطلب بالتفصيل..."
                value={requestFormData.notes}
                onChange={(e) =>
                  setRequestFormData((s) => ({ ...s, notes: e.target.value }))
                }
              />
            </div>
            <div className="space-y-2">
              <Label>إرفاق ملف (اختياري)</Label>
              <Input
                type="file"
                ref={attachmentInputRef}
                onChange={(e) =>
                  setRequestFormData((s) => ({
                    ...s,
                    attachmentName: e.target.files?.[0]?.name || '',
                  }))
                }
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleSendRequest}>إرسال الطلب</Button>
            <DialogClose asChild>
              <Button variant="outline">إلغاء</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* نافذة تأكيد تحديث فاتورة موجودة */}
      <AlertDialog open={isUpdateConfirmOpen} onOpenChange={setIsUpdateConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>تأكيد تحديث الفاتورة</AlertDialogTitle>
            <AlertDialogDescription>
              هل أنت متأكد من تحديث بيانات الفاتورة رقم {loadedSale?.soNumber}؟
              <br />
              سيؤدي هذا إلى تحديث جميع البيانات بما في ذلك الأجهزة المضافة.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>إلغاء</AlertDialogCancel>
            <AlertDialogAction onClick={confirmUpdateSale}>
              موافق، تحديث الفاتورة
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      
      {/* نافذة خيارات التصدير */}
      {/* نافذة خيارات التصدير */}
      <Dialog open={isExportModalOpen} onOpenChange={setIsExportModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>خيارات التصدير</DialogTitle>
            <DialogDescription>
              حدد الحقول التي تريد تضمينها في التقرير المصدّر
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="export-saleNumber"
                    checked={exportColumns.saleNumber}
                    onChange={(e) =>
                      setExportColumns((prev) => ({
                        ...prev,
                        saleNumber: e.target.checked,
                      }))
                    }
                    className="ml-2"
                  />
                  <Label htmlFor="export-saleNumber">رقم الفاتورة</Label>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="export-date"
                    checked={exportColumns.date}
                    onChange={(e) =>
                      setExportColumns((prev) => ({
                        ...prev,
                        date: e.target.checked,
                      }))
                    }
                    className="ml-2"
                  />
                  <Label htmlFor="export-date">تاريخ البيع</Label>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="export-client"
                    checked={exportColumns.client}
                    onChange={(e) =>
                      setExportColumns((prev) => ({
                        ...prev,
                        client: e.target.checked,
                      }))
                    }
                    className="ml-2"
                  />
                  <Label htmlFor="export-client">العميل</Label>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="export-warehouse"
                    checked={exportColumns.warehouse}
                    onChange={(e) =>
                      setExportColumns((prev) => ({
                        ...prev,
                        warehouse: e.target.checked,
                      }))
                    }
                    className="ml-2"
                  />
                  <Label htmlFor="export-warehouse">المخزن</Label>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="export-serialNumber"
                    checked={exportColumns.serialNumber}
                    onChange={(e) =>
                      setExportColumns((prev) => ({
                        ...prev,
                        serialNumber: e.target.checked,
                      }))
                    }
                    className="ml-2"
                  />
                  <Label htmlFor="export-serialNumber">الرقم التسلسلي</Label>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="export-model"
                    checked={exportColumns.model}
                    onChange={(e) =>
                      setExportColumns((prev) => ({
                        ...prev,
                        model: e.target.checked,
                      }))
                    }
                    className="ml-2"
                  />
                  <Label htmlFor="export-model">الموديل</Label>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="export-condition"
                    checked={exportColumns.condition}
                    onChange={(e) =>
                      setExportColumns((prev) => ({
                        ...prev,
                        condition: e.target.checked,
                      }))
                    }
                    className="ml-2"
                  />
                  <Label htmlFor="export-condition">حالة الجهاز</Label>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="export-warranty"
                    checked={exportColumns.warranty}
                    onChange={(e) =>
                      setExportColumns((prev) => ({
                        ...prev,
                        warranty: e.target.checked,
                      }))
                    }
                    className="ml-2"
                  />
                  <Label htmlFor="export-warranty">فترة الضمان</Label>
                </div>
                <div className="flex items-center space-x-2 space-x-reverse">
                  <input
                    type="checkbox"
                    id="export-barcode"
                    checked={exportColumns.barcode}
                    onChange={(e) =>
                      setExportColumns((prev) => ({
                        ...prev,
                        barcode: e.target.checked,
                      }))
                    }
                    className="ml-2"
                  />
                  <Label htmlFor="export-barcode">صورة الباركود</Label>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={generateCustomPdf}>
              <FileDown className="ml-2 h-4 w-4" /> تصدير إلى PDF
            </Button>
            <Button variant="outline" onClick={() => setIsExportModalOpen(false)}>
              إلغاء
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* مكون عرض المرفقات - نفس صفحة التوريد */}
      <AttachmentsViewer
        attachments={attachments}
        isOpen={isAttachmentsModalOpen}
        onClose={() => setIsAttachmentsModalOpen(false)}
        onRemove={(fileName: string) => {
          setAttachments(prev => prev.filter(file => file.fileName !== fileName));
        }}
        canDelete={canCreate || canEdit}
      />

      {/* تم استبدال النوافذ القديمة بمكون AttachmentsViewer */}


      {/* نافذة تنبيه المسودات الموجودة */}
      <AlertDialog open={isDraftWarningOpen} onOpenChange={setIsDraftWarningOpen}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <div className="w-8 h-8 bg-amber-500 text-white rounded-full flex items-center justify-center">
                ⚠️
              </div>
              مسودة فاتورة موجودة
            </AlertDialogTitle>
            <AlertDialogDescription className="text-right">
              يوجد فاتورة بيع غير مكتملة في المسودات:
              <br />
              <strong>رقم الفاتورة:</strong> {existingDraft?.soNumber}
              <br />
              <strong>العميل:</strong> {existingDraft?.clientName || 'غير محدد'}
              <br />
              <strong>عدد الأجهزة:</strong> {existingDraft?.items.length || 0}
              <br />
              <strong>تاريخ الإنشاء:</strong> {existingDraft ? new Date().toLocaleDateString('ar-EG') : ''}
              <br />
              <br />
              هل تريد استكمال المسودة الموجودة أم إنشاء فاتورة جديدة؟
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex gap-2">
            <AlertDialogCancel
              onClick={() => {
                setIsDraftWarningOpen(false);
                setExistingDraft(null);
              }}
              className="flex-1"
            >
              إلغاء
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={deleteDraftAndProceed}
              className="flex-1 bg-red-500 hover:bg-red-600 text-white"
            >
              حذف المسودة وإنشاء جديد
            </AlertDialogAction>
            <AlertDialogAction
              onClick={continueDraft}
              className="flex-1 bg-green-500 hover:bg-green-600 text-white"
            >
              استكمال المسودة
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
