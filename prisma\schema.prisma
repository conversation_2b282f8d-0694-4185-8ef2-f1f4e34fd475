// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id              Int      @id @default(autoincrement())
  email           String   @unique
  name            String?
  username        String?  @unique @default("user")
  role            String?  @default("user")
  phone           String?  @default("")
  photo           String?  @default("")
  status          String?  @default("Active")
  lastLogin       DateTime?
  branchLocation  String?  // موقع الفرع
  warehouseAccess Json?    // صلاحيات الوصول للمخازن كـ JSON array
  permissions     Json?    // للصلاحيات المخزنة كـ JSON
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt @default(now())
  posts           Post[]
  createdDatabases Database[]

  @@map("users")
}

model Post {
  id        Int     @id @default(autoincrement())
  title     String
  content   String?
  published Boolean @default(false)
  author    User    @relation(fields: [authorId], references: [id])
  authorId  Int
}

model SystemSetting {
  id              Int      @id @default(1)
  logoUrl         String   @default("")
  companyNameAr   String   @default("")
  companyNameEn   String   @default("")
  addressAr       String   @default("")
  addressEn       String   @default("")
  phone           String   @default("")
  email           String   @default("")
  website         String   @default("")
  footerTextAr    String   @default("")
  footerTextEn    String   @default("")
  updatedAt       DateTime @updatedAt @default(now())
  createdAt       DateTime @default(now())
}

model DeviceModel {
  id             Int      @id @default(autoincrement())
  name           String
  manufacturerId BigInt
  category       String   @default("هاتف ذكي")
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  @@unique([name, manufacturerId])
}

model AuditLog {
  id        Int      @id @default(autoincrement())
  timestamp DateTime @default(now())
  userId    Int
  username  String
  operation String
  details   String
}

model SupplyOrder {
  id             Int      @id @default(autoincrement())
  supplyOrderId  String   @unique
  supplierId     Int
  invoiceNumber  String?  // جعل رقم الفاتورة اختياري
  supplyDate     DateTime // ✅ تحويل من String إلى DateTime
  warehouseId    Int
  employeeName   String
  notes          String?
  invoiceFileName String?
  referenceNumber String?
  createdAt      DateTime @default(now())
  status         String?  @default("completed")

  // العلاقة مع العناصر
  items          SupplyOrderItem[]
}

model SupplyOrderItem {
  id           Int      @id @default(autoincrement())
  supplyOrderId Int
  imei         String
  model        String
  manufacturer String
  condition    String   // 'جديد' | 'مستخدم'
  createdAt    DateTime @default(now())

  // العلاقة مع أمر التوريد
  supplyOrder  SupplyOrder @relation(fields: [supplyOrderId], references: [id], onDelete: Cascade)

  @@map("supply_order_items")
}

model Sale {
  id             Int      @id @default(autoincrement())
  soNumber       String   @unique
  opNumber       String
  date           DateTime // ✅ تحويل من String إلى DateTime
  clientName     String
  warehouseName  String
  notes          String?
  warrantyPeriod String
  employeeName   String
  createdAt      DateTime @default(now())
  attachments    String?

  // العلاقة مع العناصر
  items          SaleItem[]
}

model SaleItem {
  id        Int      @id @default(autoincrement())
  saleId    Int
  deviceId  String
  model     String
  price     Float
  condition String   // 'جديد' | 'مستخدم'
  createdAt DateTime @default(now())

  // العلاقة مع المبيعة
  sale      Sale @relation(fields: [saleId], references: [id], onDelete: Cascade)

  @@map("sale_items")
}

model Device {
  id          String   @id
  model       String
  status      String
  storage     String
  price       Float
  condition   String
  warehouseId Int?
  supplierId  Int?
  dateAdded   DateTime @default(now())
  replacementInfo Json?  // معلومات الاستبدال كـ JSON
}

model Warehouse {
  id        Int      @id @default(autoincrement())
  name      String
  type      String
  location  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt @default(now())
}

model Return {
  id             Int       @id @default(autoincrement())
  roNumber       String    @unique
  opReturnNumber String
  date           DateTime  // ✅ تحويل من String إلى DateTime
  saleId         Int
  soNumber       String
  clientName     String
  warehouseName  String
  notes          String?
  status         String    @default("معلق") // معلق، مقبول، مرفوض، مكتمل
  processedBy    String?
  processedDate  DateTime? // ✅ تحويل من String إلى DateTime
  employeeName   String
  createdAt      DateTime  @default(now())
  attachments    String?

  // العلاقة مع العناصر
  items          ReturnItem[]
}

model ReturnItem {
  id                   Int      @id @default(autoincrement())
  returnId             Int
  deviceId             String
  model                String
  returnReason         String
  replacementDeviceId  String?
  isReplacement        Boolean  @default(false)
  originalDeviceId     String?
  createdAt            DateTime @default(now())

  // العلاقة مع المرتجع
  return               Return @relation(fields: [returnId], references: [id], onDelete: Cascade)

  @@map("return_items")
}

model EvaluationOrder {
  id              Int       @id @default(autoincrement())
  orderId         String    @unique
  employeeName    String
  date            DateTime  // ✅ تحويل من String إلى DateTime
  notes           String?
  status          String    @default("معلق")
  acknowledgedBy  String?
  acknowledgedDate DateTime? // ✅ تحويل من String إلى DateTime
  warehouseName   String?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt @default(now())

  // العلاقة مع العناصر
  items           EvaluationOrderItem[]

  @@map("evaluation_orders")
}

model EvaluationOrderItem {
  id              Int      @id @default(autoincrement())
  evaluationOrderId Int
  deviceId        String
  model           String
  externalGrade   String
  screenGrade     String
  networkGrade    String
  finalGrade      String
  fault           String?
  damageType      String?
  createdAt       DateTime @default(now())

  // العلاقة مع أمر التقييم
  evaluationOrder EvaluationOrder @relation(fields: [evaluationOrderId], references: [id], onDelete: Cascade)

  @@map("evaluation_order_items")
}

model Client {
  id        Int      @id @default(autoincrement())
  name      String
  phone     String?
  email     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Supplier {
  id        Int      @id @default(autoincrement())
  name      String
  phone     String?
  email     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model MaintenanceOrder {
  id                      Int      @id @default(autoincrement())
  orderNumber             String   @unique
  referenceNumber         String?
  date                    DateTime // ✅ تحويل من String إلى DateTime
  employeeName            String
  maintenanceEmployeeId   Int?
  maintenanceEmployeeName String?
  notes                   String?
  attachmentName          String?
  status                  String   @default("wip") // wip, completed
  source                  String   @default("warehouse") // warehouse, direct
  items                   String?  // JSON string of items
  createdAt               DateTime @default(now())

  // العلاقة مع العناصر (للاستخدام المستقبلي)
  itemsRelation           MaintenanceOrderItem[] @relation("MaintenanceOrderItems")
}

model MaintenanceOrderItem {
  id                 Int      @id @default(autoincrement())
  maintenanceOrderId Int
  deviceId           String
  model              String
  fault              String?
  notes              String?
  createdAt          DateTime @default(now())

  // العلاقة مع أمر الصيانة
  maintenanceOrder   MaintenanceOrder @relation("MaintenanceOrderItems", fields: [maintenanceOrderId], references: [id], onDelete: Cascade)

  @@map("maintenance_order_items")
}

model MaintenanceReceiptOrder {
  id                      Int      @id @default(autoincrement())
  receiptNumber           String   @unique
  referenceNumber         String?
  date                    DateTime // ✅ تحويل من String إلى DateTime
  employeeName            String
  maintenanceEmployeeName String?
  notes                   String?
  attachmentName          String?
  status                  String   @default("completed") // completed
  items                   String?  // JSON string of items
  createdAt               DateTime @default(now())

  // العلاقة مع العناصر (للاستخدام المستقبلي)
  itemsRelation           MaintenanceReceiptOrderItem[] @relation("MaintenanceReceiptOrderItems")
}

model MaintenanceReceiptOrderItem {
  id                        Int      @id @default(autoincrement())
  maintenanceReceiptOrderId Int
  deviceId                  String
  model                     String
  result                    String
  fault                     String?
  damage                    String?
  notes                     String?
  createdAt                 DateTime @default(now())

  // العلاقة مع إيصال الصيانة
  maintenanceReceiptOrder   MaintenanceReceiptOrder @relation("MaintenanceReceiptOrderItems", fields: [maintenanceReceiptOrderId], references: [id], onDelete: Cascade)

  @@map("maintenance_receipt_order_items")
}

model DeliveryOrder {
  id                  Int      @id @default(autoincrement())
  deliveryOrderNumber String   @unique
  referenceNumber     String?
  date                DateTime // ✅ تحويل من String إلى DateTime
  warehouseId         Int
  warehouseName       String
  employeeName        String
  notes               String?
  attachmentName      String?
  status              String   @default("completed")
  createdAt           DateTime @default(now())

  // العلاقة مع العناصر
  items               DeliveryOrderItem[]
}

model DeliveryOrderItem {
  id              Int      @id @default(autoincrement())
  deliveryOrderId Int
  deviceId        String
  model           String
  result          String
  fault           String?
  damage          String?
  notes           String?
  createdAt       DateTime @default(now())

  // العلاقة مع أمر التسليم
  deliveryOrder   DeliveryOrder @relation(fields: [deliveryOrderId], references: [id], onDelete: Cascade)

  @@map("delivery_order_items")
}

model MaintenanceLog {
  id              Int       @id @default(autoincrement())
  deviceId        String
  model           String
  repairDate      DateTime  // ✅ تحويل من String إلى DateTime
  notes           String?
  result          String?
  status          String    @default("pending") // pending, acknowledged
  acknowledgedDate DateTime? // ✅ تحويل من String إلى DateTime
  warehouseName   String?
  acknowledgedBy  String?
  createdAt       DateTime  @default(now())

  @@map("maintenance_logs")
}

model EmployeeRequest {
  id                   Int       @id @default(autoincrement())
  requestNumber        String    @unique
  requestType          String    // تعديل، حذف، إعادة نظر
  priority             String    // عادي، طاريء، طاريء جدا
  notes                String
  status               String    @default("قيد المراجعة") // قيد المراجعة، تم التنفيذ، مرفوض
  requestDate          DateTime  // ✅ تحويل من String إلى DateTime
  employeeName         String
  employeeId           Int
  relatedOrderType     String?   // sales, returns, supply, etc.
  relatedOrderId       Int?
  relatedOrderDisplayId String?  // رقم الأمر المعروض للمستخدم
  attachmentName       String?
  adminNotes           String?
  processedBy          Int?
  processedDate        DateTime? // ✅ تحويل من String إلى DateTime
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @updatedAt @default(now())

  @@map("employee_requests")
}

model InternalMessage {
  id                  Int       @id @default(autoincrement())
  threadId            Int       // معرف المحادثة
  senderId            Int       // معرف المرسل
  senderName          String    // اسم المرسل
  recipientId         Int       // معرف المستقبل (0 للجميع)
  recipientName       String    // اسم المستقبل
  recipientIds        Json?     // قائمة معرفات المستقبلين للمجموعات
  text                String    // نص الرسالة
  attachmentName      String?   // اسم المرفق
  attachmentContent   String?   // محتوى المرفق Base64 (للصور الصغيرة)
  attachmentType      String?   // نوع المرفق
  attachmentUrl       String?   // رابط المرفق
  attachmentFileName  String?   // اسم الملف المحفوظ
  attachmentSize      Int?      // حجم المرفق
  sentDate            DateTime  // ✅ تحويل من String إلى DateTime
  status              String    @default("مرسلة") // مرسلة، مقروءة، تم الرد، تم الحل
  isRead              Boolean   @default(false)
  parentMessageId     Int?      // معرف الرسالة الأصلية للردود
  employeeRequestId   Int?      // ربط بطلب موظف
  resolutionNote      String?   // ملاحظة الحل
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt @default(now())

  @@map("internal_messages")
}

// نماذج جديدة لإدارة قواعد البيانات
model DatabaseConnection {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  host        String
  port        Int      @default(5432)
  database    String
  username    String
  password    String   // مشفر
  isActive    Boolean  @default(false)
  isDefault   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  backups     DatabaseBackup[]
  databases   Database[]
  
  @@map("database_connections")
}

model DatabaseBackup {
  id           Int                @id @default(autoincrement())
  name         String
  description  String?
  filePath     String
  fileSize     String
  backupType   String             @default("manual") // manual, automatic
  status       String             @default("completed") // pending, completed, failed
  createdBy    String?
  createdAt    DateTime           @default(now())
  
  connection   DatabaseConnection @relation(fields: [connectionId], references: [id], onDelete: Cascade)
  connectionId Int
  
  @@map("database_backups")
}

model Database {
  id           Int                @id @default(autoincrement())
  name         String
  connectionId Int
  owner        String             @default("")
  template     String             @default("template0")
  encoding     String             @default("UTF8")
  createdBy    Int
  createdAt    DateTime           @default(now())
  updatedAt    DateTime           @updatedAt @default(now())
  
  // العلاقات
  connection   DatabaseConnection @relation(fields: [connectionId], references: [id], onDelete: Cascade)
  creator      User               @relation(fields: [createdBy], references: [id])
  
  @@unique([name, connectionId])
  @@map("databases")
}
