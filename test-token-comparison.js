/**
 * اختبار التوكن المُنشأ من المتجر مقابل المطلوب
 */

// محاكاة currentUser من initialUsers
const currentUser = {
  id: 1,
  name: 'مدير النظام',
  username: 'admin',
  email: '<EMAIL>'
};

// دالة getAuthHeader من المتجر
function getAuthHeaderFromStore(currentUser) {
  if (!currentUser) {
    // استخدام البيانات الافتراضية للمستخدم المديري
    return { 'Authorization': `Bearer ${btoa('user:admin:admin')}` };
  }
  // استخدام بيانات المستخدم الحالي
  const token = btoa(`user:${currentUser.username || currentUser.name}:admin`);
  return { 'Authorization': `Bearer ${token}` };
}

// التوكن المُتوقع ليعمل
function getWorkingToken() {
  return { 'Authorization': `Bearer ${btoa('user:admin:admin')}` };
}

console.log('🧪 اختبار التوكنات...\n');

// التوكن من المتجر مع currentUser
const storeToken = getAuthHeaderFromStore(currentUser);
console.log('🔑 Store token (with currentUser):', storeToken);
console.log('🔍 Decoded:', atob(storeToken.Authorization.split(' ')[1]));

// التوكن من المتجر بدون currentUser
const storeTokenNoUser = getAuthHeaderFromStore(null);
console.log('\n🔑 Store token (no currentUser):', storeTokenNoUser);
console.log('🔍 Decoded:', atob(storeTokenNoUser.Authorization.split(' ')[1]));

// التوكن الذي يعمل
const workingToken = getWorkingToken();
console.log('\n🔑 Working token:', workingToken);
console.log('🔍 Decoded:', atob(workingToken.Authorization.split(' ')[1]));

// المقارنة
console.log('\n📊 المقارنة:');
console.log('Store (with user) === Working:', storeToken.Authorization === workingToken.Authorization);
console.log('Store (no user) === Working:', storeTokenNoUser.Authorization === workingToken.Authorization);

// اختبار التوكنات مع الـ API
async function testTokens() {
  const BASE_URL = 'http://localhost:9005';
  
  console.log('\n🧪 اختبار التوكنات مع API...');
  
  const tokens = [
    { name: 'Store (with user)', token: storeToken },
    { name: 'Store (no user)', token: storeTokenNoUser },
    { name: 'Working token', token: workingToken }
  ];
  
  for (const { name, token } of tokens) {
    try {
      const response = await fetch(`${BASE_URL}/api/supply-draft`, {
        method: 'GET',
        headers: token
      });
      
      console.log(`📡 ${name}: ${response.status} ${response.statusText}`);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.log(`   ❌ Error: ${errorText}`);
      }
    } catch (error) {
      console.log(`   ❌ ${name} failed:`, error.message);
    }
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testTokens();
}

module.exports = { getAuthHeaderFromStore, getWorkingToken };
