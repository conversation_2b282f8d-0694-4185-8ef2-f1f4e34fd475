// اختبار إصلاح مشكلة المرفقات في المرتجعات
// Test for attachments fix in returns

// محاكاة البيانات المختلفة التي قد تأتي من قاعدة البيانات
const testCases = [
  {
    name: "حالة المرفقات كـ JSON string",
    returnOrder: {
      id: 1,
      attachments: '["file1.pdf", "file2.jpg"]',
      createdAt: "2024-01-01T00:00:00.000Z"
    }
  },
  {
    name: "حالة المرفقات كـ array",
    returnOrder: {
      id: 2,
      attachments: ["file1.pdf", "file2.jpg"],
      createdAt: "2024-01-01T00:00:00.000Z"
    }
  },
  {
    name: "حالة المرفقات null",
    returnOrder: {
      id: 3,
      attachments: null,
      createdAt: "2024-01-01T00:00:00.000Z"
    }
  },
  {
    name: "حالة المرفقات undefined",
    returnOrder: {
      id: 4,
      attachments: undefined,
      createdAt: "2024-01-01T00:00:00.000Z"
    }
  },
  {
    name: "حالة JSON غير صالح",
    returnOrder: {
      id: 5,
      attachments: '["file1.pdf", "file2.jpg"',  // JSON غير مكتمل
      createdAt: "2024-01-01T00:00:00.000Z"
    }
  }
];

// دالة محاكاة معالجة المرفقات (مطابقة للكود المُصلح)
function processAttachments(returnOrder) {
  if (returnOrder.attachments) {
    let attachmentsArray = [];
    
    // التعامل مع أنواع البيانات المختلفة
    if (typeof returnOrder.attachments === 'string') {
      try {
        // إذا كان string، حاول تحويله إلى JSON
        attachmentsArray = JSON.parse(returnOrder.attachments);
      } catch (error) {
        console.error('Error parsing attachments JSON:', error);
        attachmentsArray = [];
      }
    } else if (Array.isArray(returnOrder.attachments)) {
      // إذا كان مصفوفة بالفعل
      attachmentsArray = returnOrder.attachments;
    }

    if (attachmentsArray.length > 0 && typeof attachmentsArray[0] === 'string') {
      // تحويل من النوع القديم (string[]) إلى الجديد (AttachmentFile[])
      const convertedAttachments = attachmentsArray.map(fileName => ({
        originalName: fileName,
        fileName: fileName,
        filePath: `/attachments/returns/${fileName}`,
        size: 0, // حجم غير معروف للملفات القديمة
        type: 'application/octet-stream', // نوع افتراضي
        uploadedAt: returnOrder.createdAt || new Date().toISOString()
      }));
      return convertedAttachments;
    } else {
      // النوع الجديد بالفعل
      return attachmentsArray;
    }
  } else {
    return [];
  }
}

// تشغيل الاختبارات
console.log("🧪 اختبار إصلاح مشكلة المرفقات في المرتجعات");
console.log("=" .repeat(50));

testCases.forEach((testCase, index) => {
  console.log(`\n${index + 1}. ${testCase.name}:`);
  console.log("البيانات الأصلية:", testCase.returnOrder.attachments);
  
  try {
    const result = processAttachments(testCase.returnOrder);
    console.log("✅ النتيجة:", result);
    console.log("عدد المرفقات:", result.length);
  } catch (error) {
    console.log("❌ خطأ:", error.message);
  }
});

console.log("\n" + "=" .repeat(50));
console.log("✅ انتهى الاختبار");
