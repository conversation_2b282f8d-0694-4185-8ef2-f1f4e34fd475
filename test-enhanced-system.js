/**
 * اختبار النظام المحسن للصلاحيات والمخازن
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testEnhancedSystem() {
  try {
    console.log('🧪 اختبار النظام المحسن للصلاحيات...\n');

    // اختبار 1: جلب مستخدم مع صلاحياته المحسنة
    console.log('1️⃣ اختبار جلب المستخدم مع الصلاحيات...');
    const userWithPermissions = await prisma.user.findFirst({
      include: {
        userPermissions: {
          include: {
            permission: true
          }
        },
        userWarehouseAccess: {
          include: {
            warehouse: true
          }
        }
      }
    });

    if (userWithPermissions) {
      console.log(`👤 المستخدم: ${userWithPermissions.username}`);
      console.log(`📊 عدد الصلاحيات: ${userWithPermissions.userPermissions.length}`);
      console.log(`🏪 عدد المخازن: ${userWithPermissions.userWarehouseAccess.length}`);

      // عرض الصلاحيات المفعلة
      const activePermissions = userWithPermissions.userPermissions.filter(up => 
        up.canView || up.canCreate || up.canEdit || up.canDelete
      );
      
      console.log(`\n✅ الصلاحيات المفعلة (${activePermissions.length}):`);
      activePermissions.forEach(up => {
        const permissions = [];
        if (up.canView) permissions.push('عرض');
        if (up.canCreate) permissions.push('إنشاء');
        if (up.canEdit) permissions.push('تعديل');
        if (up.canDelete) permissions.push('حذف');
        
        console.log(`   - ${up.permission.displayName}: ${permissions.join(', ')}`);
      });
    } else {
      console.log('❌ لم يتم العثور على مستخدمين');
    }

    // اختبار 2: إحصائيات النظام المحسن
    console.log('\n2️⃣ إحصائيات النظام المحسن...');
    
    const stats = await Promise.all([
      prisma.permission.count(),
      prisma.userPermission.count(),
      prisma.userWarehouseAccess.count(),
      prisma.deviceReplacement.count(),
      prisma.messageRecipient.count()
    ]);

    console.log(`📊 إجمالي الصلاحيات: ${stats[0]}`);
    console.log(`📊 إجمالي صلاحيات المستخدمين: ${stats[1]}`);
    console.log(`📊 إجمالي صلاحيات المخازن: ${stats[2]}`);
    console.log(`📊 إجمالي استبدالات الأجهزة: ${stats[3]}`);
    console.log(`📊 إجمالي مستقبلي الرسائل: ${stats[4]}`);

    // اختبار 3: مقارنة النظام القديم والجديد
    console.log('\n3️⃣ مقارنة النظام القديم والجديد...');
    
    const usersWithJsonPermissions = await prisma.user.count({
      where: { permissions: { not: null } }
    });
    
    const usersWithRelationalPermissions = await prisma.user.count({
      where: {
        userPermissions: {
          some: {}
        }
      }
    });

    console.log(`📊 مستخدمين لديهم صلاحيات JSON: ${usersWithJsonPermissions}`);
    console.log(`📊 مستخدمين لديهم صلاحيات علائقية: ${usersWithRelationalPermissions}`);

    // اختبار 4: اختبار الأداء
    console.log('\n4️⃣ اختبار الأداء...');
    
    const startTime = Date.now();
    
    const usersWithComplexQuery = await prisma.user.findMany({
      where: {
        userPermissions: {
          some: {
            canView: true,
            permission: {
              category: 'sales'
            }
          }
        }
      },
      include: {
        userPermissions: {
          include: {
            permission: true
          },
          where: {
            OR: [
              { canView: true },
              { canCreate: true },
              { canEdit: true },
              { canDelete: true }
            ]
          }
        }
      }
    });
    
    const endTime = Date.now();
    
    console.log(`⚡ استغرق الاستعلام المعقد: ${endTime - startTime}ms`);
    console.log(`👥 وجد ${usersWithComplexQuery.length} مستخدم لديه صلاحيات المبيعات`);

    // اختبار 5: اختبار العلاقات
    console.log('\n5️⃣ اختبار العلاقات...');
    
    const permissionsWithUsers = await prisma.permission.findMany({
      include: {
        userPermissions: {
          include: {
            user: {
              select: { id: true, username: true, name: true }
            }
          }
        }
      },
      where: {
        userPermissions: {
          some: {}
        }
      }
    });

    console.log(`🔗 صلاحيات لها مستخدمين: ${permissionsWithUsers.length}`);
    
    permissionsWithUsers.slice(0, 3).forEach(perm => {
      console.log(`   - ${perm.displayName}: ${perm.userPermissions.length} مستخدم`);
    });

    console.log('\n✅ تم اكتمال جميع الاختبارات بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testEnhancedSystem();
