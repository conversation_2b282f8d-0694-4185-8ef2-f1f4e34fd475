-- Migration script لإضافة النماذج الجديدة مع الحفاظ على البيانات الموجودة
-- يجب تشغيل هذا Script قبل تطبيق schema الجديد

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول الصلاحيات
CREATE TABLE IF NOT EXISTS "permissions" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "displayName" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "description" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "permissions_pkey" PRIMARY KEY ("id")
);

-- إنشاء فهرس فريد على اسم الصلاحية
CREATE UNIQUE INDEX IF NOT EXISTS "permissions_name_key" ON "permissions"("name");

-- إن<PERSON>اء جدول صلاحيات المستخدمين
CREATE TABLE IF NOT EXISTS "user_permissions" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "permissionId" INTEGER NOT NULL,
    "canView" BOOLEAN NOT NULL DEFAULT false,
    "canCreate" BOOLEAN NOT NULL DEFAULT false,
    "canEdit" BOOLEAN NOT NULL DEFAULT false,
    "canDelete" BOOLEAN NOT NULL DEFAULT false,
    "canViewAll" BOOLEAN NOT NULL DEFAULT false,
    "canManage" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_permissions_pkey" PRIMARY KEY ("id")
);

-- إنشاء فهرس فريد على userId و permissionId
CREATE UNIQUE INDEX IF NOT EXISTS "user_permissions_userId_permissionId_key" ON "user_permissions"("userId", "permissionId");

-- إنشاء جدول صلاحيات المخازن
CREATE TABLE IF NOT EXISTS "user_warehouse_access" (
    "id" SERIAL NOT NULL,
    "userId" INTEGER NOT NULL,
    "warehouseId" INTEGER NOT NULL,
    "accessType" TEXT NOT NULL DEFAULT 'read',
    "canTransfer" BOOLEAN NOT NULL DEFAULT false,
    "canAudit" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_warehouse_access_pkey" PRIMARY KEY ("id")
);

-- إنشاء فهرس فريد على userId و warehouseId
CREATE UNIQUE INDEX IF NOT EXISTS "user_warehouse_access_userId_warehouseId_key" ON "user_warehouse_access"("userId", "warehouseId");

-- إنشاء جدول استبدالات الأجهزة
CREATE TABLE IF NOT EXISTS "device_replacements" (
    "id" SERIAL NOT NULL,
    "originalDeviceId" TEXT NOT NULL,
    "replacementDeviceId" TEXT NOT NULL,
    "reason" TEXT NOT NULL,
    "replacementDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "notes" TEXT,
    "status" TEXT NOT NULL DEFAULT 'active',
    "processedBy" TEXT,
    "approvedBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "device_replacements_pkey" PRIMARY KEY ("id")
);

-- إنشاء فهرس فريد على originalDeviceId و replacementDeviceId
CREATE UNIQUE INDEX IF NOT EXISTS "device_replacements_originalDeviceId_replacementDeviceId_key" ON "device_replacements"("originalDeviceId", "replacementDeviceId");

-- إنشاء جدول مستقبلي الرسائل
CREATE TABLE IF NOT EXISTS "message_recipients" (
    "id" SERIAL NOT NULL,
    "messageId" INTEGER NOT NULL,
    "userId" INTEGER NOT NULL,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "readAt" TIMESTAMP(3),

    CONSTRAINT "message_recipients_pkey" PRIMARY KEY ("id")
);

-- إنشاء فهرس فريد على messageId و userId
CREATE UNIQUE INDEX IF NOT EXISTS "message_recipients_messageId_userId_key" ON "message_recipients"("messageId", "userId");

-- إضافة العلاقات الخارجية
ALTER TABLE "user_permissions" ADD CONSTRAINT "user_permissions_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "user_permissions" ADD CONSTRAINT "user_permissions_permissionId_fkey" FOREIGN KEY ("permissionId") REFERENCES "permissions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "user_warehouse_access" ADD CONSTRAINT "user_warehouse_access_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "user_warehouse_access" ADD CONSTRAINT "user_warehouse_access_warehouseId_fkey" FOREIGN KEY ("warehouseId") REFERENCES "Warehouse"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "device_replacements" ADD CONSTRAINT "device_replacements_originalDeviceId_fkey" FOREIGN KEY ("originalDeviceId") REFERENCES "Device"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "device_replacements" ADD CONSTRAINT "device_replacements_replacementDeviceId_fkey" FOREIGN KEY ("replacementDeviceId") REFERENCES "Device"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "message_recipients" ADD CONSTRAINT "message_recipients_messageId_fkey" FOREIGN KEY ("messageId") REFERENCES "internal_messages"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "message_recipients" ADD CONSTRAINT "message_recipients_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- إدراج الصلاحيات الافتراضية
INSERT INTO "permissions" ("name", "displayName", "category") VALUES
('dashboard', 'لوحة التحكم', 'core'),
('track', 'التتبع', 'core'),
('clients', 'العملاء', 'sales'),
('suppliers', 'الموردين', 'supply'),
('inventory', 'المخزون', 'inventory'),
('supply', 'التوريد', 'supply'),
('sales', 'المبيعات', 'sales'),
('returns', 'المرتجعات', 'sales'),
('evaluationOrders', 'أوامر التقييم', 'inventory'),
('maintenance', 'الصيانة', 'maintenance'),
('maintenanceTransfer', 'تحويل الصيانة', 'maintenance'),
('maintenanceReceipt', 'استلام الصيانة', 'maintenance'),
('deliveryOrders', 'أوامر التسليم', 'delivery'),
('warehouses', 'المخازن', 'inventory'),
('users', 'المستخدمين', 'admin'),
('reports', 'التقارير', 'reports'),
('stocktaking', 'الجرد', 'inventory'),
('acceptDevices', 'استلام الأجهزة', 'inventory'),
('settings', 'الإعدادات', 'admin'),
('requests', 'الطلبات', 'core'),
('messaging', 'الرسائل', 'communication')
ON CONFLICT ("name") DO NOTHING;
