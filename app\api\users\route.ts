import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, checkRelationsBeforeDelete } from '@/lib/transaction-utils';
import { 
  getAllUsersWithPermissions, 
  getUserWithEnhancedPermissions, 
  updateUserPermissions, 
  updateUserWarehouseAccess 
} from '@/lib/enhanced-permissions';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض - السماح للمدراء والأدمن
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const url = new URL(request.url);
    const userId = url.searchParams.get('userId');

    if (userId) {
      // جلب مستخدم واحد مع صلاحياته المحسنة
      const user = await getUserWithEnhancedPermissions(parseInt(userId));
      if (!user) {
        return NextResponse.json({ error: 'المستخدم غير موجود' }, { status: 404 });
      }
      return NextResponse.json(user);
    } else {
      // جلب جميع المستخدمين مع صلاحياتهم المحسنة
      const users = await getAllUsersWithPermissions();
      return NextResponse.json(users);
    }
  } catch (error) {
    console.error('Failed to fetch users:', error);
    return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض - السماح للمدراء والأدمن
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newUser = await request.json();
    const { permissions, warehouseAccess, ...userData } = newUser;

    // Basic validation
    if (!userData.name || !userData.email || !userData.username) {
      return NextResponse.json(
        { error: 'Name, email, and username are required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if email already exists
      const existingUserByEmail = await tx.user.findUnique({
        where: { email: userData.email }
      });

      if (existingUserByEmail) {
        throw new Error('Email already exists');
      }

      // Check if username already exists
      const existingUserByUsername = await tx.user.findUnique({
        where: { username: userData.username }
      });

      if (existingUserByUsername) {
        throw new Error('Username already exists');
      }

      // Create new user
      const user = await tx.user.create({
        data: {
          name: userData.name,
          email: userData.email,
          username: userData.username,
          role: userData.role || 'user',
          phone: userData.phone || '',
          photo: userData.photo || '',
          status: userData.status || 'Active',
          branchLocation: userData.branchLocation || null,
          // الاحتفاظ بحقول JSON مؤقتاً للتوافق مع النظام الحالي
          warehouseAccess: warehouseAccess ? JSON.stringify(warehouseAccess) : undefined,
          permissions: permissions ? JSON.stringify(permissions) : undefined,
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created user: ${user.name} (${user.email})`,
      });

      return user;
    });

    // تحديث الصلاحيات والمخازن باستخدام النظام المحسن
    if (permissions) {
      await updateUserPermissions(result.id, permissions);
    }

    if (warehouseAccess) {
      await updateUserWarehouseAccess(result.id, warehouseAccess);
    }

    // إرجاع المستخدم مع صلاحياته المحسنة
    const userWithPermissions = await getUserWithEnhancedPermissions(result.id);
    return NextResponse.json(userWithPermissions, { status: 201 });
  } catch (error) {
    console.error('Failed to create user:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Email already exists') {
        return NextResponse.json({ error: 'Email already exists' }, { status: 409 });
      }
      if (error.message === 'Username already exists') {
        return NextResponse.json({ error: 'Username already exists' }, { status: 409 });
      }
    }
    
    return NextResponse.json({ error: 'Failed to create user' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض - السماح للمدراء والأدمن
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedUser = await request.json();
    
    if (!updatedUser.id) {
      return NextResponse.json(
        { message: 'User ID is required' },
        { status: 400 }
    );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if user exists
      const existingUser = await tx.user.findUnique({
        where: { id: updatedUser.id }
      });

      if (!existingUser) {
        throw new Error('User not found');
      }

      // Check if new email already exists (excluding current user)
      if (updatedUser.email && updatedUser.email !== existingUser.email) {
        const emailExists = await tx.user.findUnique({
          where: { email: updatedUser.email }
        });

        if (emailExists) {
          throw new Error('Email already exists');
        }
      }

      // Check if new username already exists (excluding current user)
      if (updatedUser.username && updatedUser.username !== existingUser.username) {
        const usernameExists = await tx.user.findUnique({
          where: { username: updatedUser.username }
        });

        if (usernameExists) {
          throw new Error('Username already exists');
        }
      }

      // Update user
      const user = await tx.user.update({
        where: { id: updatedUser.id },
        data: {
          name: updatedUser.name || existingUser.name,
          email: updatedUser.email || existingUser.email,
          username: updatedUser.username !== undefined ? updatedUser.username : existingUser.username,
          role: updatedUser.role !== undefined ? updatedUser.role : existingUser.role,
          phone: updatedUser.phone !== undefined ? updatedUser.phone : existingUser.phone,
          photo: updatedUser.photo !== undefined ? updatedUser.photo : existingUser.photo,
          status: updatedUser.status !== undefined ? updatedUser.status : existingUser.status,
          branchLocation: updatedUser.branchLocation !== undefined ? updatedUser.branchLocation : existingUser.branchLocation,
          warehouseAccess: updatedUser.warehouseAccess !== undefined ? 
            (typeof updatedUser.warehouseAccess === 'object' ? JSON.stringify(updatedUser.warehouseAccess) : updatedUser.warehouseAccess) : 
            existingUser.warehouseAccess,
          permissions: updatedUser.permissions !== undefined ? 
            (typeof updatedUser.permissions === 'object' ? JSON.stringify(updatedUser.permissions) : updatedUser.permissions) : 
            existingUser.permissions,
          lastLogin: updatedUser.lastLogin !== undefined ? updatedUser.lastLogin : existingUser.lastLogin,
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated user: ${user.name} (${user.email})`,
      });

      return user;
    });

    // معالجة حقول JSON قبل الإرسال
    const processedUser = {
      ...result,
      permissions: result.permissions ?
        (typeof result.permissions === 'string' ?
          JSON.parse(result.permissions) : result.permissions) : null,
      warehouseAccess: result.warehouseAccess ?
        (typeof result.warehouseAccess === 'string' ?
          JSON.parse(result.warehouseAccess) : result.warehouseAccess) : null,
    };

    return NextResponse.json(processedUser);
  } catch (error) {
    console.error('Failed to update user:', error);
    
    if (error instanceof Error) {
      if (error.message === 'User not found') {
        return NextResponse.json({ message: 'User not found' }, { status: 404 });
      }
      if (error.message === 'Email already exists') {
        return NextResponse.json({ message: 'Email already exists' }, { status: 409 });
      }
      if (error.message === 'Username already exists') {
        return NextResponse.json({ message: 'Username already exists' }, { status: 409 });
      }
    }
    
    return NextResponse.json({ error: 'Failed to update user' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية عليا لحذف المستخدمين
    const authResult = await requireAuth(request, 'admin');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { message: 'User ID is required' },
        { status: 400 }
    );
    }

    // منع حذف المستخدم لنفسه
    if (id.toString() === authResult.user!.id) {
      return NextResponse.json(
        { message: 'Cannot delete your own account' },
        { status: 403 }
    );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if user exists
      const existingUser = await tx.user.findUnique({
        where: { id }
      });

      if (!existingUser) {
        throw new Error('User not found');
      }

      // Protect Super Admin (assuming Super Admin has id: 1 or role: 'admin')
      if (id === 1 || existingUser.role === 'admin') {
        throw new Error('Cannot delete admin user');
      }

      // فحص العلاقات قبل الحذف (إذا كان هناك audit logs مرتبطة بالمستخدم)
      const relatedAuditLogs = await tx.auditLog.count({
        where: { userId: id.toString() }
      });

      // لا نحذف المستخدم إذا كان له audit logs، بل نعطله فقط
      if (relatedAuditLogs > 0) {
        const updatedUser = await tx.user.update({
          where: { id },
          data: {
            status: 'Inactive'
          }
        });

        // إنشاء audit log
        await createAuditLogInTransaction(tx, {
          userId: authResult.user!.id,
          username: authResult.user!.username,
          operation: 'DEACTIVATE',
          details: `Deactivated user: ${existingUser.name} (${existingUser.email}) - has related audit logs`,
        });

        return {
          message: 'User deactivated successfully (has related audit logs)',
          action: 'deactivated'
        };
      } else {
        // Delete user if no related records
        await tx.user.delete({
          where: { id }
        });

        // إنشاء audit log
        await createAuditLogInTransaction(tx, {
          userId: authResult.user!.id,
          username: authResult.user!.username,
          operation: 'DELETE',
          details: `Deleted user: ${existingUser.name} (${existingUser.email})`,
        });

        return {
          message: 'User deleted successfully',
          action: 'deleted'
        };
      }
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete user:', error);

    if (error instanceof Error) {
      if (error.message === 'User not found') {
        return NextResponse.json({ message: 'User not found' }, { status: 404 });
      }
      if (error.message === 'Cannot delete admin user') {
        return NextResponse.json({ message: 'Cannot delete admin user' }, { status: 403 });
      }
    }

    return NextResponse.json({ error: 'Failed to delete user' }, { status: 500 });
  }
}
