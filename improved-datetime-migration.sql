-- تحسين سكريبت ترحيل حقول التاريخ
-- يتعامل مع الأخطاء المحتملة في البيانات الموجودة

BEGIN;

-- إنشاء دالة مساعدة لتحويل النصوص إلى timestamps بشكل آمن
CREATE OR REPLACE FUNCTION safe_parse_date(input_text TEXT) 
RETURNS TIMESTAMP AS $$
BEGIN
    -- إذا كان النص فارغ أو null، استخدم التاريخ الحالي
    IF input_text IS NULL OR input_text = '' OR input_text = 'null' THEN
        RETURN NOW();
    END IF;
    
    -- محاولة تحويل التاريخ مع معالجة الأشكال المختلفة
    BEGIN
        -- إزالة الأحرف الغير صالحة
        DECLARE
            cleaned_date TEXT;
        BEGIN
            -- تنظيف النص
            cleaned_date := TRIM(input_text);
            
            -- إذ<PERSON> كان يحتوي على تاريخ صالح
            IF cleaned_date ~ '^\d{4}-\d{2}-\d{2}' THEN
                -- إضافة الوقت إذا لم يكن موجود
                IF cleaned_date !~ 'T\d{2}:\d{2}:\d{2}' THEN
                    cleaned_date := cleaned_date || 'T00:00:00';
                END IF;
                
                -- محاولة التحويل
                RETURN cleaned_date::TIMESTAMP;
            END IF;
            
            -- في حالة عدم التطابق، استخدم التاريخ الحالي
            RETURN NOW();
        END;
    EXCEPTION WHEN others THEN
        -- في حالة أي خطأ، استخدم التاريخ الحالي
        RETURN NOW();
    END;
END;
$$ LANGUAGE plpgsql;

-- تحديث جدول SupplyOrder
DO $$ 
BEGIN
    -- التحقق من وجود العمود والجدول
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'SupplyOrder' AND column_name = 'supplyDate') THEN
        
        -- إنشاء عمود مؤقت
        ALTER TABLE "SupplyOrder" ADD COLUMN IF NOT EXISTS supplyDate_new TIMESTAMP;
        
        -- تحديث البيانات
        UPDATE "SupplyOrder" 
        SET supplyDate_new = safe_parse_date("supplyDate");
        
        -- إزالة العمود القديم وإعادة تسمية الجديد
        ALTER TABLE "SupplyOrder" DROP COLUMN IF EXISTS "supplyDate";
        ALTER TABLE "SupplyOrder" RENAME COLUMN supplyDate_new TO "supplyDate";
    END IF;
END $$;

-- تحديث جدول Sale
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'Sale' AND column_name = 'date') THEN
        
        ALTER TABLE "Sale" ADD COLUMN IF NOT EXISTS date_new TIMESTAMP;
        UPDATE "Sale" SET date_new = safe_parse_date("date");
        ALTER TABLE "Sale" DROP COLUMN IF EXISTS "date";
        ALTER TABLE "Sale" RENAME COLUMN date_new TO "date";
    END IF;
END $$;

-- تحديث جدول Return
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'Return' AND column_name = 'returnDate') THEN
        
        ALTER TABLE "Return" ADD COLUMN IF NOT EXISTS returnDate_new TIMESTAMP;
        UPDATE "Return" SET returnDate_new = safe_parse_date("returnDate");
        ALTER TABLE "Return" DROP COLUMN IF EXISTS "returnDate";
        ALTER TABLE "Return" RENAME COLUMN returnDate_new TO "returnDate";
    END IF;
END $$;

-- تحديث جدول MaintenanceOrder
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'MaintenanceOrder' AND column_name = 'orderDate') THEN
        
        ALTER TABLE "MaintenanceOrder" ADD COLUMN IF NOT EXISTS orderDate_new TIMESTAMP;
        UPDATE "MaintenanceOrder" SET orderDate_new = safe_parse_date("orderDate");
        ALTER TABLE "MaintenanceOrder" DROP COLUMN IF EXISTS "orderDate";
        ALTER TABLE "MaintenanceOrder" RENAME COLUMN orderDate_new TO "orderDate";
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'MaintenanceOrder' AND column_name = 'expectedCompletionDate') THEN
        
        ALTER TABLE "MaintenanceOrder" ADD COLUMN IF NOT EXISTS expectedCompletionDate_new TIMESTAMP;
        UPDATE "MaintenanceOrder" SET expectedCompletionDate_new = safe_parse_date("expectedCompletionDate");
        ALTER TABLE "MaintenanceOrder" DROP COLUMN IF EXISTS "expectedCompletionDate";
        ALTER TABLE "MaintenanceOrder" RENAME COLUMN expectedCompletionDate_new TO "expectedCompletionDate";
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'MaintenanceOrder' AND column_name = 'actualCompletionDate') THEN
        
        ALTER TABLE "MaintenanceOrder" ADD COLUMN IF NOT EXISTS actualCompletionDate_new TIMESTAMP;
        UPDATE "MaintenanceOrder" SET actualCompletionDate_new = safe_parse_date("actualCompletionDate");
        ALTER TABLE "MaintenanceOrder" DROP COLUMN IF EXISTS "actualCompletionDate";
        ALTER TABLE "MaintenanceOrder" RENAME COLUMN actualCompletionDate_new TO "actualCompletionDate";
    END IF;
END $$;

-- تحديث باقي الجداول...
DO $$ 
BEGIN
    -- تحديث جدول EmployeeRequest
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'employee_requests' AND column_name = 'requestDate') THEN
        
        ALTER TABLE "employee_requests" ADD COLUMN IF NOT EXISTS requestDate_new TIMESTAMP;
        UPDATE "employee_requests" SET requestDate_new = safe_parse_date("requestDate");
        ALTER TABLE "employee_requests" DROP COLUMN IF EXISTS "requestDate";
        ALTER TABLE "employee_requests" RENAME COLUMN requestDate_new TO "requestDate";
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'employee_requests' AND column_name = 'responseDate') THEN
        
        ALTER TABLE "employee_requests" ADD COLUMN IF NOT EXISTS responseDate_new TIMESTAMP;
        UPDATE "employee_requests" SET responseDate_new = safe_parse_date("responseDate");
        ALTER TABLE "employee_requests" DROP COLUMN IF EXISTS "responseDate";
        ALTER TABLE "employee_requests" RENAME COLUMN responseDate_new TO "responseDate";
    END IF;
    
    -- تحديث User.lastLogin
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'User' AND column_name = 'lastLogin') THEN
        
        ALTER TABLE "User" ADD COLUMN IF NOT EXISTS lastLogin_new TIMESTAMP;
        UPDATE "User" SET lastLogin_new = safe_parse_date("lastLogin");
        ALTER TABLE "User" DROP COLUMN IF EXISTS "lastLogin";
        ALTER TABLE "User" RENAME COLUMN lastLogin_new TO "lastLogin";
    END IF;
    
    -- تحديث InternalMessage.sentDate
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'internal_messages' AND column_name = 'sentDate') THEN
        
        ALTER TABLE "internal_messages" ADD COLUMN IF NOT EXISTS sentDate_new TIMESTAMP;
        UPDATE "internal_messages" SET sentDate_new = safe_parse_date("sentDate");
        ALTER TABLE "internal_messages" DROP COLUMN IF EXISTS "sentDate";
        ALTER TABLE "internal_messages" RENAME COLUMN sentDate_new TO "sentDate";
    END IF;
END $$;

-- حذف الدالة المساعدة
DROP FUNCTION IF EXISTS safe_parse_date(TEXT);

COMMIT;

-- عرض رسالة نجح
DO $$ 
BEGIN
    RAISE NOTICE 'تم تحويل جميع حقول التاريخ بنجاح إلى DateTime';
END $$;
