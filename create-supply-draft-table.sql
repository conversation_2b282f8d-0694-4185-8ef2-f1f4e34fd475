-- إنشاء جدول المسودات لأوامر التوريد
CREATE TABLE IF NOT EXISTS supply_order_drafts (
    id SERIAL PRIMARY KEY,
    "userId" INTEGER NOT NULL,
    "formState" TEXT NOT NULL,
    "currentItems" TEXT NOT NULL,
    attachments TEXT,
    "supplyOrderId" VARCHAR(255),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- إضافة foreign key constraint إذا كان جدول users موجود
    CONSTRAINT fk_supply_draft_user 
        FOREIGN KEY ("userId") 
        REFERENCES users(id) 
        ON DELETE CASCADE
);

-- إنشاء index للبحث السريع
CREATE INDEX IF NOT EXISTS idx_supply_draft_user_id ON supply_order_drafts("userId");
CREATE INDEX IF NOT EXISTS idx_supply_draft_updated_at ON supply_order_drafts("updatedAt");

-- <PERSON><PERSON><PERSON><PERSON><PERSON> trigger لتحديث updatedAt تلقائياً
CREATE OR REPLACE FUNCTION update_supply_draft_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_supply_draft_updated_at
    BEFORE UPDATE ON supply_order_drafts
    FOR EACH ROW
    EXECUTE FUNCTION update_supply_draft_updated_at();
