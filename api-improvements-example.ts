/**
 * مثال على تحديث API endpoints لاستخدام النماذج العلائقية الجديدة
 * بدلاً من حقول JSON
 */

// مثال على تحديث API المستخدمين

// ===== قبل التحسين (استخدام JSON) =====

/*
// الكود القديم
const user = await prisma.user.findUnique({
  where: { id: userId },
  select: {
    id: true,
    name: true,
    email: true,
    username: true,
    role: true,
    permissions: true,        // JSON field
    warehouseAccess: true,    // JSON array
  }
});

// التحقق من الصلاحيات
const hasPermission = (user, permissionName, action) => {
  if (!user.permissions || typeof user.permissions !== 'object') {
    return false;
  }
  const permission = user.permissions[permissionName];
  return permission && permission[action] === true;
};

// التحقق من صلاحية المخزن
const hasWarehouseAccess = (user, warehouseId) => {
  if (!user.warehouseAccess || !Array.isArray(user.warehouseAccess)) {
    return false;
  }
  return user.warehouseAccess.includes(warehouseId);
};
*/

// ===== بعد التحسين (استخدام النماذج العلائقية) =====

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// دالة للحصول على مستخدم مع صلاحياته
export async function getUserWithPermissions(userId: number) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      userPermissions: {
        include: {
          permission: true
        }
      },
      warehouseAccess: {
        include: {
          warehouse: true
        }
      }
    }
  });

  if (!user) {
    return null;
  }

  // تحويل الصلاحيات إلى تنسيق سهل الاستخدام
  const permissions = user.userPermissions.reduce((acc, userPerm) => {
    acc[userPerm.permission.name] = {
      view: userPerm.canView,
      create: userPerm.canCreate,
      edit: userPerm.canEdit,
      delete: userPerm.canDelete,
      viewAll: userPerm.canViewAll,
      manage: userPerm.canManage
    };
    return acc;
  }, {} as Record<string, any>);

  // تحويل صلاحيات المخازن إلى تنسيق سهل الاستخدام
  const warehouseAccess = user.warehouseAccess.map(access => ({
    warehouseId: access.warehouseId,
    warehouseName: access.warehouse.name,
    accessType: access.accessType,
    canTransfer: access.canTransfer,
    canAudit: access.canAudit
  }));

  return {
    ...user,
    permissions,
    warehouseAccess,
    userPermissions: undefined, // إخفاء التفاصيل الداخلية
  };
}

// دالة للتحقق من الصلاحيات
export function hasPermission(
  user: any,
  permissionName: string,
  action: 'view' | 'create' | 'edit' | 'delete' | 'viewAll' | 'manage'
): boolean {
  if (!user.permissions || !user.permissions[permissionName]) {
    return false;
  }
  return user.permissions[permissionName][action] === true;
}

// دالة للتحقق من صلاحية المخزن
export function hasWarehouseAccess(
  user: any,
  warehouseId: number,
  requiredAccess: 'read' | 'write' | 'admin' = 'read'
): boolean {
  if (!user.warehouseAccess || !Array.isArray(user.warehouseAccess)) {
    return false;
  }

  const access = user.warehouseAccess.find(
    (wa: any) => wa.warehouseId === warehouseId
  );

  if (!access) {
    return false;
  }

  // التحقق من مستوى الصلاحية المطلوب
  const accessLevels = { read: 1, write: 2, admin: 3 };
  const userLevel = accessLevels[access.accessType as keyof typeof accessLevels] || 0;
  const requiredLevel = accessLevels[requiredAccess];

  return userLevel >= requiredLevel;
}

// دالة لتحديث صلاحيات المستخدم
export async function updateUserPermissions(
  userId: number,
  permissions: Record<string, any>
) {
  // حذف الصلاحيات الحالية
  await prisma.userPermission.deleteMany({
    where: { userId }
  });

  // إضافة الصلاحيات الجديدة
  const permissionPromises = Object.entries(permissions).map(async ([permName, permData]) => {
    // البحث عن الصلاحية أو إنشاؤها
    const permission = await prisma.permission.upsert({
      where: { name: permName },
      update: {},
      create: {
        name: permName,
        displayName: permName,
        category: 'general'
      }
    });

    // إنشاء صلاحية المستخدم
    return prisma.userPermission.create({
      data: {
        userId,
        permissionId: permission.id,
        canView: permData.view || false,
        canCreate: permData.create || false,
        canEdit: permData.edit || false,
        canDelete: permData.delete || false,
        canViewAll: permData.viewAll || false,
        canManage: permData.manage || false,
      }
    });
  });

  await Promise.all(permissionPromises);
}

// دالة لتحديث صلاحيات المخازن
export async function updateUserWarehouseAccess(
  userId: number,
  warehouseAccess: { warehouseId: number; accessType: string; canTransfer?: boolean; canAudit?: boolean }[]
) {
  // حذف الصلاحيات الحالية
  await prisma.userWarehouseAccess.deleteMany({
    where: { userId }
  });

  // إضافة الصلاحيات الجديدة
  const accessPromises = warehouseAccess.map(access =>
    prisma.userWarehouseAccess.create({
      data: {
        userId,
        warehouseId: access.warehouseId,
        accessType: access.accessType,
        canTransfer: access.canTransfer || false,
        canAudit: access.canAudit || false,
      }
    })
  );

  await Promise.all(accessPromises);
}

// مثال على API endpoint محدث
export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const userId = url.searchParams.get('userId');

    if (userId) {
      // الحصول على مستخدم واحد مع صلاحياته
      const user = await getUserWithPermissions(parseInt(userId));
      if (!user) {
        return Response.json({ error: 'المستخدم غير موجود' }, { status: 404 });
      }
      return Response.json(user);
    } else {
      // الحصول على جميع المستخدمين مع صلاحياتهم
      const users = await prisma.user.findMany({
        include: {
          userPermissions: {
            include: {
              permission: true
            }
          },
          warehouseAccess: {
            include: {
              warehouse: true
            }
          }
        }
      });

      // تحويل البيانات لتنسيق مناسب
      const formattedUsers = users.map(user => {
        const permissions = user.userPermissions.reduce((acc, userPerm) => {
          acc[userPerm.permission.name] = {
            view: userPerm.canView,
            create: userPerm.canCreate,
            edit: userPerm.canEdit,
            delete: userPerm.canDelete,
            viewAll: userPerm.canViewAll,
            manage: userPerm.canManage
          };
          return acc;
        }, {} as Record<string, any>);

        const warehouseAccess = user.warehouseAccess.map(access => ({
          warehouseId: access.warehouseId,
          warehouseName: access.warehouse.name,
          accessType: access.accessType,
          canTransfer: access.canTransfer,
          canAudit: access.canAudit
        }));

        return {
          ...user,
          permissions,
          warehouseAccess,
          userPermissions: undefined,
        };
      });

      return Response.json(formattedUsers);
    }
  } catch (error) {
    console.error('خطأ في جلب المستخدمين:', error);
    return Response.json({ error: 'خطأ في الخادم' }, { status: 500 });
  }
}

export async function PUT(request: Request) {
  try {
    const data = await request.json();
    const { id, permissions, warehouseAccess, ...userData } = data;

    if (!id) {
      return Response.json({ error: 'معرف المستخدم مطلوب' }, { status: 400 });
    }

    // تحديث بيانات المستخدم الأساسية
    const updatedUser = await prisma.user.update({
      where: { id },
      data: userData
    });

    // تحديث الصلاحيات إذا تم توفيرها
    if (permissions) {
      await updateUserPermissions(id, permissions);
    }

    // تحديث صلاحيات المخازن إذا تم توفيرها
    if (warehouseAccess) {
      await updateUserWarehouseAccess(id, warehouseAccess);
    }

    // إرجاع المستخدم المحدث مع صلاحياته
    const userWithPermissions = await getUserWithPermissions(id);
    return Response.json(userWithPermissions);

  } catch (error) {
    console.error('خطأ في تحديث المستخدم:', error);
    return Response.json({ error: 'خطأ في الخادم' }, { status: 500 });
  }
}

// مثال على API للحصول على صلاحيات المستخدم الحالي
export async function getCurrentUserPermissions(userId: number) {
  const user = await getUserWithPermissions(userId);
  
  if (!user) {
    return null;
  }

  return {
    userId: user.id,
    username: user.username,
    role: user.role,
    permissions: user.permissions,
    warehouseAccess: user.warehouseAccess
  };
}

// مثال على التحقق من الصلاحيات في middleware
export async function checkPermission(
  userId: number,
  permissionName: string,
  action: string
): Promise<boolean> {
  const user = await getUserWithPermissions(userId);
  
  if (!user) {
    return false;
  }

  return hasPermission(user, permissionName, action as any);
}

// مثال على التحقق من صلاحية المخزن في API
export async function checkWarehousePermission(
  userId: number,
  warehouseId: number,
  requiredAccess: 'read' | 'write' | 'admin' = 'read'
): Promise<boolean> {
  const user = await getUserWithPermissions(userId);
  
  if (!user) {
    return false;
  }

  return hasWarehouseAccess(user, warehouseId, requiredAccess);
}
