-- Migration لتحويل حقول التاريخ من String إلى DateTime
-- يجب تشغيل هذا Migration بحذر على قاعدة البيانات

BEGIN;

-- تحديث SupplyOrder.supplyDate
ALTER TABLE "SupplyOrder" 
ALTER COLUMN "supplyDate" TYPE TIMESTAMP USING 
CASE 
    WHEN "supplyDate" ~ '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}' THEN "supplyDate"::TIMESTAMP
    WHEN "supplyDate" ~ '^\d{4}-\d{2}-\d{2}' THEN ("supplyDate" || 'T00:00:00')::TIMESTAMP
    ELSE NOW()
END;

-- تحديث Sale.date
ALTER TABLE "Sale" 
ALTER COLUMN "date" TYPE TIMESTAMP USING 
CASE 
    WHEN "date" ~ '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}' THEN "date"::TIMESTAMP
    WHEN "date" ~ '^\d{4}-\d{2}-\d{2}' THEN ("date" || 'T00:00:00')::TIMESTAMP
    ELSE NOW()
END;

-- تحديث Return.date
ALTER TABLE "Return" 
ALTER COLUMN "date" TYPE TIMESTAMP USING 
CASE 
    WHEN "date" ~ '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}' THEN "date"::TIMESTAMP
    WHEN "date" ~ '^\d{4}-\d{2}-\d{2}' THEN ("date" || 'T00:00:00')::TIMESTAMP
    ELSE NOW()
END;

-- تحديث Return.processedDate (nullable)
ALTER TABLE "Return" 
ALTER COLUMN "processedDate" TYPE TIMESTAMP USING 
CASE 
    WHEN "processedDate" IS NULL THEN NULL
    WHEN "processedDate" ~ '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}' THEN "processedDate"::TIMESTAMP
    WHEN "processedDate" ~ '^\d{4}-\d{2}-\d{2}' THEN ("processedDate" || 'T00:00:00')::TIMESTAMP
    ELSE NULL
END;

-- تحديث EvaluationOrder.date
ALTER TABLE "evaluation_orders" 
ALTER COLUMN "date" TYPE TIMESTAMP USING 
CASE 
    WHEN "date" ~ '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}' THEN "date"::TIMESTAMP
    WHEN "date" ~ '^\d{4}-\d{2}-\d{2}' THEN ("date" || 'T00:00:00')::TIMESTAMP
    ELSE NOW()
END;

-- تحديث EvaluationOrder.acknowledgedDate (nullable)
ALTER TABLE "evaluation_orders" 
ALTER COLUMN "acknowledgedDate" TYPE TIMESTAMP USING 
CASE 
    WHEN "acknowledgedDate" IS NULL THEN NULL
    WHEN "acknowledgedDate" ~ '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}' THEN "acknowledgedDate"::TIMESTAMP
    WHEN "acknowledgedDate" ~ '^\d{4}-\d{2}-\d{2}' THEN ("acknowledgedDate" || 'T00:00:00')::TIMESTAMP
    ELSE NULL
END;

-- تحديث MaintenanceOrder.date
ALTER TABLE "MaintenanceOrder" 
ALTER COLUMN "date" TYPE TIMESTAMP USING 
CASE 
    WHEN "date" ~ '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}' THEN "date"::TIMESTAMP
    WHEN "date" ~ '^\d{4}-\d{2}-\d{2}' THEN ("date" || 'T00:00:00')::TIMESTAMP
    ELSE NOW()
END;

-- تحديث MaintenanceReceiptOrder.date
ALTER TABLE "MaintenanceReceiptOrder" 
ALTER COLUMN "date" TYPE TIMESTAMP USING 
CASE 
    WHEN "date" ~ '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}' THEN "date"::TIMESTAMP
    WHEN "date" ~ '^\d{4}-\d{2}-\d{2}' THEN ("date" || 'T00:00:00')::TIMESTAMP
    ELSE NOW()
END;

-- تحديث DeliveryOrder.date
ALTER TABLE "DeliveryOrder" 
ALTER COLUMN "date" TYPE TIMESTAMP USING 
CASE 
    WHEN "date" ~ '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}' THEN "date"::TIMESTAMP
    WHEN "date" ~ '^\d{4}-\d{2}-\d{2}' THEN ("date" || 'T00:00:00')::TIMESTAMP
    ELSE NOW()
END;

-- تحديث MaintenanceLog.repairDate
ALTER TABLE "maintenance_logs" 
ALTER COLUMN "repairDate" TYPE TIMESTAMP USING 
CASE 
    WHEN "repairDate" ~ '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}' THEN "repairDate"::TIMESTAMP
    WHEN "repairDate" ~ '^\d{4}-\d{2}-\d{2}' THEN ("repairDate" || 'T00:00:00')::TIMESTAMP
    ELSE NOW()
END;

-- تحديث MaintenanceLog.acknowledgedDate (nullable)
ALTER TABLE "maintenance_logs" 
ALTER COLUMN "acknowledgedDate" TYPE TIMESTAMP USING 
CASE 
    WHEN "acknowledgedDate" IS NULL THEN NULL
    WHEN "acknowledgedDate" ~ '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}' THEN "acknowledgedDate"::TIMESTAMP
    WHEN "acknowledgedDate" ~ '^\d{4}-\d{2}-\d{2}' THEN ("acknowledgedDate" || 'T00:00:00')::TIMESTAMP
    ELSE NULL
END;

-- تحديث EmployeeRequest.requestDate
ALTER TABLE "employee_requests" 
ALTER COLUMN "requestDate" TYPE TIMESTAMP USING 
CASE 
    WHEN "requestDate" ~ '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}' THEN "requestDate"::TIMESTAMP
    WHEN "requestDate" ~ '^\d{4}-\d{2}-\d{2}' THEN ("requestDate" || 'T00:00:00')::TIMESTAMP
    ELSE NOW()
END;

-- تحديث EmployeeRequest.processedDate (nullable)
ALTER TABLE "employee_requests" 
ALTER COLUMN "processedDate" TYPE TIMESTAMP USING 
CASE 
    WHEN "processedDate" IS NULL THEN NULL
    WHEN "processedDate" ~ '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}' THEN "processedDate"::TIMESTAMP
    WHEN "processedDate" ~ '^\d{4}-\d{2}-\d{2}' THEN ("processedDate" || 'T00:00:00')::TIMESTAMP
    ELSE NULL
END;

-- تحديث InternalMessage.sentDate
ALTER TABLE "internal_messages" 
ALTER COLUMN "sentDate" TYPE TIMESTAMP USING 
CASE 
    WHEN "sentDate" ~ '^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}' THEN "sentDate"::TIMESTAMP
    WHEN "sentDate" ~ '^\d{4}-\d{2}-\d{2}' THEN ("sentDate" || 'T00:00:00')::TIMESTAMP
    ELSE NOW()
END;

COMMIT;
