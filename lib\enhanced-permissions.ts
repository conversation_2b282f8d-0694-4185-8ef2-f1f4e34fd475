import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface UserWithPermissions {
  id: number;
  username: string | null;
  name: string | null;
  email: string;
  role: string | null;
  permissions: Record<string, {
    view: boolean;
    create: boolean;
    edit: boolean;
    delete: boolean;
    viewAll: boolean;
    manage: boolean;
  }>;
  warehouseAccess: {
    warehouseId: number;
    warehouseName: string;
    accessType: string;
    canTransfer: boolean;
    canAudit: boolean;
  }[];
}

/**
 * جلب مستخدم مع صلاحياته المحسنة
 */
export async function getUserWithEnhancedPermissions(userId: number): Promise<UserWithPermissions | null> {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      userPermissions: {
        include: {
          permission: true
        }
      },
      userWarehouseAccess: {
        include: {
          warehouse: true
        }
      }
    }
  });

  if (!user) {
    return null;
  }

  // تحويل الصلاحيات إلى تنسيق سهل الاستخدام
  const permissions = user.userPermissions.reduce((acc, userPerm) => {
    acc[userPerm.permission.name] = {
      view: userPerm.canView,
      create: userPerm.canCreate,
      edit: userPerm.canEdit,
      delete: userPerm.canDelete,
      viewAll: userPerm.canViewAll,
      manage: userPerm.canManage
    };
    return acc;
  }, {} as Record<string, any>);

  // تحويل صلاحيات المخازن إلى تنسيق سهل الاستخدام
  const warehouseAccess = user.userWarehouseAccess.map(access => ({
    warehouseId: access.warehouseId,
    warehouseName: access.warehouse.name,
    accessType: access.accessType,
    canTransfer: access.canTransfer,
    canAudit: access.canAudit
  }));

  return {
    id: user.id,
    username: user.username,
    name: user.name,
    email: user.email,
    role: user.role,
    permissions,
    warehouseAccess
  };
}

/**
 * التحقق من صلاحية معينة للمستخدم
 */
export function hasPermission(
  user: UserWithPermissions,
  permissionName: string,
  action: 'view' | 'create' | 'edit' | 'delete' | 'viewAll' | 'manage'
): boolean {
  if (!user.permissions || !user.permissions[permissionName]) {
    return false;
  }
  return user.permissions[permissionName][action] === true;
}

/**
 * التحقق من صلاحية الوصول للمخزن
 */
export function hasWarehouseAccess(
  user: UserWithPermissions,
  warehouseId: number,
  requiredAccess: 'read' | 'write' | 'admin' = 'read'
): boolean {
  if (!user.warehouseAccess || !Array.isArray(user.warehouseAccess)) {
    return false;
  }

  const access = user.warehouseAccess.find(
    wa => wa.warehouseId === warehouseId
  );

  if (!access) {
    return false;
  }

  // التحقق من مستوى الصلاحية المطلوب
  const accessLevels = { read: 1, write: 2, admin: 3 };
  const userLevel = accessLevels[access.accessType as keyof typeof accessLevels] || 0;
  const requiredLevel = accessLevels[requiredAccess];

  return userLevel >= requiredLevel;
}

/**
 * تحديث صلاحيات المستخدم
 */
export async function updateUserPermissions(
  userId: number,
  permissions: Record<string, {
    view?: boolean;
    create?: boolean;
    edit?: boolean;
    delete?: boolean;
    viewAll?: boolean;
    manage?: boolean;
  }>
) {
  // حذف الصلاحيات الحالية
  await prisma.userPermission.deleteMany({
    where: { userId }
  });

  // إضافة الصلاحيات الجديدة
  const permissionPromises = Object.entries(permissions).map(async ([permName, permData]) => {
    // البحث عن الصلاحية أو إنشاؤها
    const permission = await prisma.permission.upsert({
      where: { name: permName },
      update: {},
      create: {
        name: permName,
        displayName: permName,
        category: 'general'
      }
    });

    // إنشاء صلاحية المستخدم فقط إذا كان لديه أي صلاحية
    const hasAnyPermission = permData.view || permData.create || permData.edit || permData.delete || permData.viewAll || permData.manage;
    
    if (hasAnyPermission) {
      return prisma.userPermission.create({
        data: {
          userId,
          permissionId: permission.id,
          canView: permData.view || false,
          canCreate: permData.create || false,
          canEdit: permData.edit || false,
          canDelete: permData.delete || false,
          canViewAll: permData.viewAll || false,
          canManage: permData.manage || false,
        }
      });
    }
    return null;
  });

  await Promise.all(permissionPromises);
}

/**
 * تحديث صلاحيات المخازن للمستخدم
 */
export async function updateUserWarehouseAccess(
  userId: number,
  warehouseAccess: { 
    warehouseId: number; 
    accessType: string; 
    canTransfer?: boolean; 
    canAudit?: boolean;
  }[]
) {
  // حذف الصلاحيات الحالية
  await prisma.userWarehouseAccess.deleteMany({
    where: { userId }
  });

  // إضافة الصلاحيات الجديدة
  const accessPromises = warehouseAccess.map(access =>
    prisma.userWarehouseAccess.create({
      data: {
        userId,
        warehouseId: access.warehouseId,
        accessType: access.accessType,
        canTransfer: access.canTransfer || false,
        canAudit: access.canAudit || false,
      }
    })
  );

  await Promise.all(accessPromises);
}

/**
 * الحصول على جميع المستخدمين مع صلاحياتهم
 */
export async function getAllUsersWithPermissions(): Promise<UserWithPermissions[]> {
  const users = await prisma.user.findMany({
    include: {
      userPermissions: {
        include: {
          permission: true
        }
      },
      userWarehouseAccess: {
        include: {
          warehouse: true
        }
      }
    }
  });

  return users.map(user => {
    const permissions = user.userPermissions.reduce((acc, userPerm) => {
      acc[userPerm.permission.name] = {
        view: userPerm.canView,
        create: userPerm.canCreate,
        edit: userPerm.canEdit,
        delete: userPerm.canDelete,
        viewAll: userPerm.canViewAll,
        manage: userPerm.canManage
      };
      return acc;
    }, {} as Record<string, any>);

    const warehouseAccess = user.userWarehouseAccess.map(access => ({
      warehouseId: access.warehouseId,
      warehouseName: access.warehouse.name,
      accessType: access.accessType,
      canTransfer: access.canTransfer,
      canAudit: access.canAudit
    }));

    return {
      id: user.id,
      username: user.username,
      name: user.name,
      email: user.email,
      role: user.role,
      permissions,
      warehouseAccess
    };
  });
}

/**
 * التحقق من صلاحية المستخدم لعملية معينة (للاستخدام في API)
 */
export async function checkUserPermission(
  userId: number,
  permissionName: string,
  action: string
): Promise<boolean> {
  const user = await getUserWithEnhancedPermissions(userId);
  
  if (!user) {
    return false;
  }

  return hasPermission(user, permissionName, action as any);
}

/**
 * التحقق من صلاحية المخزن للمستخدم (للاستخدام في API)
 */
export async function checkUserWarehousePermission(
  userId: number,
  warehouseId: number,
  requiredAccess: 'read' | 'write' | 'admin' = 'read'
): Promise<boolean> {
  const user = await getUserWithEnhancedPermissions(userId);
  
  if (!user) {
    return false;
  }

  return hasWarehouseAccess(user, warehouseId, requiredAccess);
}
