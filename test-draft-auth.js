/**
 * اختبار بسيط للتحقق من التفويض في نظام المسودات
 */

const BASE_URL = 'http://localhost:9005';

// إنشاء توكن تفويض مثل الذي يستخدمه التطبيق
function createAuthToken(username = 'admin', role = 'admin') {
  const tokenData = `user:${username}:${role}`;
  return btoa(tokenData); // ترميز base64
}

async function testDraftAuth() {
  console.log('🧪 اختبار التفويض في نظام المسودات...\n');

  try {
    const authToken = createAuthToken();
    console.log('🔑 Token created:', authToken);
    console.log('🔍 Token decoded:', atob(authToken));

    const authHeaders = {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json'
    };

    console.log('📡 Headers:', authHeaders);

    // اختبار GET
    console.log('\n1️⃣ اختبار GET /api/supply-draft...');
    const getResponse = await fetch(`${BASE_URL}/api/supply-draft`, {
      method: 'GET',
      headers: authHeaders
    });

    console.log('📊 GET Response status:', getResponse.status);
    console.log('📊 GET Response headers:', Object.fromEntries(getResponse.headers.entries()));

    if (getResponse.ok) {
      const getData = await getResponse.json();
      console.log('✅ GET Success:', getData);
    } else {
      const errorText = await getResponse.text();
      console.log('❌ GET Error:', errorText);
    }

    // اختبار POST
    console.log('\n2️⃣ اختبار POST /api/supply-draft...');
    const testDraftData = {
      formState: {
        supplierId: '1',
        warehouseId: '1',
        employeeName: 'Test User',
        invoiceNumber: 'TEST-001',
        supplyDate: new Date().toISOString().split('T')[0],
        notes: 'اختبار المسودة'
      },
      currentItems: [
        {
          imei: 'TEST123456789',
          manufacturer: 'Test Brand',
          model: 'Test Model',
          condition: 'جديد'
        }
      ],
      attachments: [],
      supplyOrderId: 'TEST-DRAFT-001',
      timestamp: new Date().toISOString()
    };

    const postResponse = await fetch(`${BASE_URL}/api/supply-draft`, {
      method: 'POST',
      headers: authHeaders,
      body: JSON.stringify(testDraftData)
    });

    console.log('📊 POST Response status:', postResponse.status);

    if (postResponse.ok) {
      const postData = await postResponse.json();
      console.log('✅ POST Success:', postData);
    } else {
      const errorText = await postResponse.text();
      console.log('❌ POST Error:', errorText);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// تشغيل الاختبار
if (require.main === module) {
  testDraftAuth();
}

module.exports = { testDraftAuth };
