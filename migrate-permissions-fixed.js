const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// mapping للصلاحيات غير المطابقة
const permissionMapping = {
  'grading': 'evaluationOrders',
  'warehouseTransfer': 'warehouses',
  'pricing': 'sales'
};

async function migrateUserPermissionsFixed() {
  try {
    console.log('🔍 بدء ترحيل صلاحيات المستخدمين المحسن...');
    
    const users = await prisma.user.findMany({
      where: {
        permissions: { not: null }
      }
    });

    console.log(`وجد ${users.length} مستخدم لديه صلاحيات JSON`);

    for (const user of users) {
      console.log(`\n👤 معالجة المستخدم: ${user.username || user.name} (ID: ${user.id})`);
      console.log(`نوع بيانات الصلاحيات: ${typeof user.permissions}`);
      
      if (user.permissions) {
        let permissions;
        
        // التعامل مع البيانات سواء كانت string أو object
        if (typeof user.permissions === 'string') {
          try {
            permissions = JSON.parse(user.permissions);
            console.log('✅ تم تحويل البيانات من JSON string');
          } catch (error) {
            console.log('❌ خطأ في تحويل JSON:', error.message);
            continue;
          }
        } else if (typeof user.permissions === 'object') {
          permissions = user.permissions;
          console.log('✅ البيانات من نوع object');
        } else {
          console.log('❌ نوع بيانات غير معروف');
          continue;
        }
        
        console.log(`📝 عدد الصلاحيات للمعالجة: ${Object.keys(permissions).length}`);
        let migratedCount = 0;
        
        for (const [permName, permData] of Object.entries(permissions)) {
          if (typeof permData === 'object' && permData !== null) {
            // تطبيق mapping إذا لزم الأمر
            const mappedPermName = permissionMapping[permName] || permName;
            
            console.log(`🔧 معالجة: ${permName} ${mappedPermName !== permName ? `-> ${mappedPermName}` : ''}`);
            
            // البحث عن الصلاحية
            const permissionRecord = await prisma.permission.findUnique({
              where: { name: mappedPermName }
            });

            if (permissionRecord) {
              try {
                const result = await prisma.userPermission.upsert({
                  where: {
                    userId_permissionId: {
                      userId: user.id,
                      permissionId: permissionRecord.id
                    }
                  },
                  update: {
                    canView: !!permData.view,
                    canCreate: !!permData.create,
                    canEdit: !!(permData.edit || permData.update),
                    canDelete: !!permData.delete,
                    canViewAll: !!permData.viewAll,
                    canManage: !!permData.manage,
                  },
                  create: {
                    userId: user.id,
                    permissionId: permissionRecord.id,
                    canView: !!permData.view,
                    canCreate: !!permData.create,
                    canEdit: !!(permData.edit || permData.update),
                    canDelete: !!permData.delete,
                    canViewAll: !!permData.viewAll,
                    canManage: !!permData.manage,
                  }
                });
                
                const hasAnyPermission = permData.view || permData.create || permData.edit || permData.update || permData.delete;
                
                console.log(`   ✅ ${permissionRecord.displayName} - (${hasAnyPermission ? 'لديه صلاحيات' : 'بدون صلاحيات'})`);
                migratedCount++;
              } catch (error) {
                console.log(`   ❌ خطأ: ${error.message}`);
              }
            } else {
              console.log(`   ⚠️ صلاحية غير موجودة: ${mappedPermName}`);
            }
          }
        }
        
        console.log(`📊 تم ترحيل ${migratedCount} صلاحية للمستخدم ${user.username}`);
      }
    }

    // التحقق من النتائج
    console.log('\n🔍 التحقق من النتائج...');
    const userPermissions = await prisma.userPermission.findMany({
      include: {
        permission: true,
        user: {
          select: { id: true, username: true, name: true }
        }
      }
    });

    console.log(`📊 إجمالي الصلاحيات المرحلة: ${userPermissions.length}`);
    
    // تجميع حسب المستخدم
    const userGroups = {};
    userPermissions.forEach(up => {
      const username = up.user.username || up.user.name;
      if (!userGroups[username]) {
        userGroups[username] = [];
      }
      userGroups[username].push(up);
    });

    for (const [username, permissions] of Object.entries(userGroups)) {
      console.log(`\n👤 ${username}:`);
      permissions.forEach(up => {
        const hasAnyPerm = up.canView || up.canCreate || up.canEdit || up.canDelete;
        if (hasAnyPerm) {
          console.log(`   ✅ ${up.permission.displayName}: عرض=${up.canView}, إنشاء=${up.canCreate}, تعديل=${up.canEdit}, حذف=${up.canDelete}`);
        }
      });
    }

  } catch (error) {
    console.error('❌ خطأ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

migrateUserPermissionsFixed();
