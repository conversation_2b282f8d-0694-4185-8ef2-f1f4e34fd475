/**
 * Script لترحيل البيانات من حقول JSON إلى النماذج العلائقية الجديدة
 * يجب تشغيل هذا Script بعد إضافة النماذج الجديدة وقبل إزالة حقول JSON
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// الصلاحيات الافتراضية المعروفة في النظام
const DEFAULT_PERMISSIONS = [
  { name: 'dashboard', displayName: 'لوحة التحكم', category: 'core' },
  { name: 'track', displayName: 'التتبع', category: 'core' },
  { name: 'clients', displayName: 'العملاء', category: 'sales' },
  { name: 'suppliers', displayName: 'الموردين', category: 'supply' },
  { name: 'inventory', displayName: 'المخزون', category: 'inventory' },
  { name: 'supply', displayName: 'التوريد', category: 'supply' },
  { name: 'sales', displayName: 'المبيعات', category: 'sales' },
  { name: 'returns', displayName: 'المرتجعات', category: 'sales' },
  { name: 'evaluationOrders', displayName: 'أوامر التقييم', category: 'inventory' },
  { name: 'maintenance', displayName: 'الصيانة', category: 'maintenance' },
  { name: 'maintenanceTransfer', displayName: 'تحويل الصيانة', category: 'maintenance' },
  { name: 'maintenanceReceipt', displayName: 'استلام الصيانة', category: 'maintenance' },
  { name: 'deliveryOrders', displayName: 'أوامر التسليم', category: 'delivery' },
  { name: 'warehouses', displayName: 'المخازن', category: 'inventory' },
  { name: 'users', displayName: 'المستخدمين', category: 'admin' },
  { name: 'reports', displayName: 'التقارير', category: 'reports' },
  { name: 'stocktaking', displayName: 'الجرد', category: 'inventory' },
  { name: 'acceptDevices', displayName: 'استلام الأجهزة', category: 'inventory' },
  { name: 'settings', displayName: 'الإعدادات', category: 'admin' },
  { name: 'requests', displayName: 'الطلبات', category: 'core' },
  { name: 'messaging', displayName: 'الرسائل', category: 'communication' }
];

async function migrateUserPermissions() {
  console.log('🔄 بدء ترحيل صلاحيات المستخدمين...');
  
  try {
    // إنشاء الصلاحيات الافتراضية
    console.log('📝 إنشاء الصلاحيات الافتراضية...');
    for (const permission of DEFAULT_PERMISSIONS) {
      await prisma.permission.upsert({
        where: { name: permission.name },
        update: {
          displayName: permission.displayName,
          category: permission.category
        },
        create: {
          name: permission.name,
          displayName: permission.displayName,
          category: permission.category
        }
      });
    }
    console.log('✅ تم إنشاء الصلاحيات الافتراضية');

    // جلب المستخدمين الذين لديهم صلاحيات JSON
    const users = await prisma.user.findMany({
      where: {
        permissions: { not: null }
      }
    });

    console.log(`👥 وجد ${users.length} مستخدم لديه صلاحيات للترحيل`);

    for (const user of users) {
      if (user.permissions && typeof user.permissions === 'object') {
        console.log(`🔄 ترحيل صلاحيات المستخدم: ${user.name || user.username}`);
        
        const permissions = user.permissions;
        
        for (const [permName, permData] of Object.entries(permissions)) {
          if (typeof permData === 'object' && permData !== null) {
            // البحث عن الصلاحية في قاعدة البيانات
            const permissionRecord = await prisma.permission.findUnique({
              where: { name: permName }
            });

            if (permissionRecord) {
              const permissionData = permData;
              
              await prisma.userPermission.upsert({
                where: {
                  userId_permissionId: {
                    userId: user.id,
                    permissionId: permissionRecord.id
                  }
                },
                update: {
                  canView: permissionData.view || false,
                  canCreate: permissionData.create || false,
                  canEdit: permissionData.edit || permissionData.update || false,
                  canDelete: permissionData.delete || false,
                  canViewAll: permissionData.viewAll || false,
                  canManage: permissionData.manage || false,
                },
                create: {
                  userId: user.id,
                  permissionId: permissionRecord.id,
                  canView: permissionData.view || false,
                  canCreate: permissionData.create || false,
                  canEdit: permissionData.edit || permissionData.update || false,
                  canDelete: permissionData.delete || false,
                  canViewAll: permissionData.viewAll || false,
                  canManage: permissionData.manage || false,
                }
              });
            } else {
              console.log(`⚠️  صلاحية غير معروفة: ${permName} للمستخدم ${user.username}`);
            }
          }
        }
        
        console.log(`✅ تم ترحيل صلاحيات المستخدم: ${user.name || user.username}`);
      }
    }

    console.log('✅ تم ترحيل جميع صلاحيات المستخدمين بنجاح');
    
  } catch (error) {
    console.error('❌ خطأ في ترحيل الصلاحيات:', error);
    throw error;
  }
}

async function migrateWarehouseAccess() {
  console.log('🔄 بدء ترحيل صلاحيات الوصول للمخازن...');
  
  try {
    const users = await prisma.user.findMany({
      where: {
        warehouseAccess: { not: null }
      }
    });

    console.log(`🏪 وجد ${users.length} مستخدم لديه صلاحيات مخازن للترحيل`);

    for (const user of users) {
      if (user.warehouseAccess && Array.isArray(user.warehouseAccess)) {
        console.log(`🔄 ترحيل صلاحيات مخازن المستخدم: ${user.name || user.username}`);
        
        const warehouseIds = user.warehouseAccess;
        
        for (const warehouseId of warehouseIds) {
          // التحقق من وجود المخزن
          const warehouse = await prisma.warehouse.findUnique({
            where: { id: warehouseId }
          });

          if (warehouse) {
            await prisma.userWarehouseAccess.upsert({
              where: {
                userId_warehouseId: {
                  userId: user.id,
                  warehouseId: warehouseId
                }
              },
              update: {
                accessType: 'write',
                canTransfer: true,
                canAudit: user.role === 'admin'
              },
              create: {
                userId: user.id,
                warehouseId: warehouseId,
                accessType: 'write',
                canTransfer: true,
                canAudit: user.role === 'admin'
              }
            });
          } else {
            console.log(`⚠️  مخزن غير موجود: ${warehouseId} للمستخدم ${user.username}`);
          }
        }
        
        console.log(`✅ تم ترحيل صلاحيات مخازن المستخدم: ${user.name || user.username}`);
      }
    }

    console.log('✅ تم ترحيل جميع صلاحيات المخازن بنجاح');
    
  } catch (error) {
    console.error('❌ خطأ في ترحيل صلاحيات المخازن:', error);
    throw error;
  }
}

async function migrateMessageRecipients() {
  console.log('🔄 بدء ترحيل مستقبلي الرسائل...');
  
  try {
    const messages = await prisma.internalMessage.findMany({
      where: {
        recipientIds: { not: null }
      }
    });

    console.log(`💬 وجد ${messages.length} رسالة لديها مستقبلين للترحيل`);

    for (const message of messages) {
      if (message.recipientIds && Array.isArray(message.recipientIds)) {
        console.log(`🔄 ترحيل مستقبلي الرسالة: ${message.id}`);
        
        const recipientIds = message.recipientIds;
        
        for (const userId of recipientIds) {
          // التحقق من وجود المستخدم
          const user = await prisma.user.findUnique({
            where: { id: userId }
          });

          if (user) {
            await prisma.messageRecipient.upsert({
              where: {
                messageId_userId: {
                  messageId: message.id,
                  userId: userId
                }
              },
              update: {
                isRead: message.isRead || false,
                readAt: message.isRead ? message.updatedAt : null
              },
              create: {
                messageId: message.id,
                userId: userId,
                isRead: message.isRead || false,
                readAt: message.isRead ? message.updatedAt : null
              }
            });
          } else {
            console.log(`⚠️  مستخدم غير موجود: ${userId} للرسالة ${message.id}`);
          }
        }
        
        console.log(`✅ تم ترحيل مستقبلي الرسالة: ${message.id}`);
      }
    }

    console.log('✅ تم ترحيل جميع مستقبلي الرسائل بنجاح');
    
  } catch (error) {
    console.error('❌ خطأ في ترحيل مستقبلي الرسائل:', error);
    throw error;
  }
}

async function migrateDeviceReplacements() {
  console.log('🔄 بدء ترحيل معلومات استبدال الأجهزة...');
  
  try {
    const devices = await prisma.device.findMany({
      where: {
        replacementInfo: { not: null }
      }
    });

    console.log(`📱 وجد ${devices.length} جهاز لديه معلومات استبدال للترحيل`);

    for (const device of devices) {
      if (device.replacementInfo && typeof device.replacementInfo === 'object') {
        console.log(`🔄 ترحيل معلومات استبدال الجهاز: ${device.id}`);
        
        const replacementInfo = device.replacementInfo;
        
        // التحقق من وجود الجهاز البديل
        if (replacementInfo.replacementDeviceId) {
          const replacementDevice = await prisma.device.findUnique({
            where: { id: replacementInfo.replacementDeviceId }
          });

          if (replacementDevice) {
            await prisma.deviceReplacement.create({
              data: {
                originalDeviceId: device.id,
                replacementDeviceId: replacementInfo.replacementDeviceId,
                reason: replacementInfo.reason || 'غير محدد',
                replacementDate: replacementInfo.date ? new Date(replacementInfo.date) : new Date(),
                notes: replacementInfo.notes || null,
                status: replacementInfo.status || 'active',
                processedBy: replacementInfo.processedBy || null,
                approvedBy: replacementInfo.approvedBy || null
              }
            });
          } else {
            console.log(`⚠️  جهاز بديل غير موجود: ${replacementInfo.replacementDeviceId} للجهاز ${device.id}`);
          }
        }
        
        console.log(`✅ تم ترحيل معلومات استبدال الجهاز: ${device.id}`);
      }
    }

    console.log('✅ تم ترحيل جميع معلومات استبدال الأجهزة بنجاح');
    
  } catch (error) {
    console.error('❌ خطأ في ترحيل معلومات استبدال الأجهزة:', error);
    throw error;
  }
}

async function verifyMigration() {
  console.log('🔍 بدء التحقق من صحة الترحيل...');
  
  try {
    // إحصائيات الصلاحيات
    const permissionsCount = await prisma.permission.count();
    const userPermissionsCount = await prisma.userPermission.count();
    console.log(`📊 إجمالي الصلاحيات: ${permissionsCount}`);
    console.log(`📊 إجمالي صلاحيات المستخدمين: ${userPermissionsCount}`);

    // إحصائيات صلاحيات المخازن
    const warehouseAccessCount = await prisma.userWarehouseAccess.count();
    console.log(`📊 إجمالي صلاحيات المخازن: ${warehouseAccessCount}`);

    // إحصائيات مستقبلي الرسائل
    const messageRecipientsCount = await prisma.messageRecipient.count();
    console.log(`📊 إجمالي مستقبلي الرسائل: ${messageRecipientsCount}`);

    // إحصائيات استبدال الأجهزة
    const deviceReplacementsCount = await prisma.deviceReplacement.count();
    console.log(`📊 إجمالي استبدالات الأجهزة: ${deviceReplacementsCount}`);

    // التحقق من البيانات المفقودة
    const usersWithJsonPermissions = await prisma.user.count({
      where: { permissions: { not: null } }
    });
    
    const usersWithJsonWarehouse = await prisma.user.count({
      where: { warehouseAccess: { not: null } }
    });

    if (usersWithJsonPermissions > 0) {
      console.log(`⚠️  يوجد ${usersWithJsonPermissions} مستخدم لم يتم ترحيل صلاحياته`);
    }

    if (usersWithJsonWarehouse > 0) {
      console.log(`⚠️  يوجد ${usersWithJsonWarehouse} مستخدم لم يتم ترحيل صلاحيات مخازنه`);
    }

    console.log('✅ تم التحقق من صحة الترحيل');
    
  } catch (error) {
    console.error('❌ خطأ في التحقق من صحة الترحيل:', error);
    throw error;
  }
}

async function main() {
  console.log('🚀 بدء عملية ترحيل البيانات من JSON إلى النماذج العلائقية');
  
  try {
    await migrateUserPermissions();
    await migrateWarehouseAccess();
    await migrateMessageRecipients();
    await migrateDeviceReplacements();
    await verifyMigration();
    
    console.log('🎉 تم إكمال عملية الترحيل بنجاح!');
    console.log('💡 يمكنك الآن إزالة حقول JSON من النماذج بأمان');
    
  } catch (error) {
    console.error('💥 فشلت عملية الترحيل:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل Script إذا تم استدعاؤه مباشرة
if (require.main === module) {
  main();
}

module.exports = {
  migrateUserPermissions,
  migrateWarehouseAccess,
  migrateMessageRecipients,
  migrateDeviceReplacements,
  verifyMigration
};
